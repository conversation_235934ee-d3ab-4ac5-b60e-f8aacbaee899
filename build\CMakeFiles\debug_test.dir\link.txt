E:\cmake\bin\cmake.exe -E rm -f CMakeFiles\debug_test.dir/objects.a
D:\msys64\mingw64\bin\ar.exe qc CMakeFiles\debug_test.dir/objects.a @CMakeFiles\debug_test.dir\objects1.rsp
D:\msys64\mingw64\bin\g++.exe  -Wall -Wextra -Wpedantic -Wl,--whole-archive CMakeFiles\debug_test.dir/objects.a -Wl,--no-whole-archive -o debug_test.exe -Wl,--out-implib,libdebug_test.dll.a -Wl,--major-image-version,0,--minor-image-version,0 @CMakeFiles\debug_test.dir\linkLibs.rsp
