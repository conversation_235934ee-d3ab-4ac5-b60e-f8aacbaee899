# CMAKE generated file: DO NOT EDIT!
# Generated by "MinGW Makefiles" Generator, CMake Version 3.30

# Delete rule output on recipe failure.
.DELETE_ON_ERROR:

#=============================================================================
# Special targets provided by cmake.

# Disable implicit rules so canonical targets will work.
.SUFFIXES:

# Disable VCS-based implicit rules.
% : %,v

# Disable VCS-based implicit rules.
% : RCS/%

# Disable VCS-based implicit rules.
% : RCS/%,v

# Disable VCS-based implicit rules.
% : SCCS/s.%

# Disable VCS-based implicit rules.
% : s.%

.SUFFIXES: .hpux_make_needs_suffix_list

# Command-line flag to silence nested $(MAKE).
$(VERBOSE)MAKESILENT = -s

#Suppress display of executed commands.
$(VERBOSE).SILENT:

# A target that is always out of date.
cmake_force:
.PHONY : cmake_force

#=============================================================================
# Set environment variables for the build.

SHELL = cmd.exe

# The CMake executable.
CMAKE_COMMAND = E:\cmake\bin\cmake.exe

# The command to remove a file.
RM = E:\cmake\bin\cmake.exe -E rm -f

# Escaping for special characters.
EQUALS = =

# The top-level source directory on which CMake was run.
CMAKE_SOURCE_DIR = F:\Projects\DualKeyEncryption

# The top-level build directory on which CMake was run.
CMAKE_BINARY_DIR = F:\Projects\DualKeyEncryption\build

# Include any dependencies generated for this target.
include CMakeFiles/dual_rsa_crypto.dir/depend.make
# Include any dependencies generated by the compiler for this target.
include CMakeFiles/dual_rsa_crypto.dir/compiler_depend.make

# Include the progress variables for this target.
include CMakeFiles/dual_rsa_crypto.dir/progress.make

# Include the compile flags for this target's objects.
include CMakeFiles/dual_rsa_crypto.dir/flags.make

CMakeFiles/dual_rsa_crypto.dir/src/dual_rsa_crypto.cpp.obj: CMakeFiles/dual_rsa_crypto.dir/flags.make
CMakeFiles/dual_rsa_crypto.dir/src/dual_rsa_crypto.cpp.obj: CMakeFiles/dual_rsa_crypto.dir/includes_CXX.rsp
CMakeFiles/dual_rsa_crypto.dir/src/dual_rsa_crypto.cpp.obj: F:/Projects/DualKeyEncryption/src/dual_rsa_crypto.cpp
CMakeFiles/dual_rsa_crypto.dir/src/dual_rsa_crypto.cpp.obj: CMakeFiles/dual_rsa_crypto.dir/compiler_depend.ts
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green --progress-dir=F:\Projects\DualKeyEncryption\build\CMakeFiles --progress-num=$(CMAKE_PROGRESS_1) "Building CXX object CMakeFiles/dual_rsa_crypto.dir/src/dual_rsa_crypto.cpp.obj"
	D:\msys64\mingw64\bin\g++.exe $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -MD -MT CMakeFiles/dual_rsa_crypto.dir/src/dual_rsa_crypto.cpp.obj -MF CMakeFiles\dual_rsa_crypto.dir\src\dual_rsa_crypto.cpp.obj.d -o CMakeFiles\dual_rsa_crypto.dir\src\dual_rsa_crypto.cpp.obj -c F:\Projects\DualKeyEncryption\src\dual_rsa_crypto.cpp

CMakeFiles/dual_rsa_crypto.dir/src/dual_rsa_crypto.cpp.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Preprocessing CXX source to CMakeFiles/dual_rsa_crypto.dir/src/dual_rsa_crypto.cpp.i"
	D:\msys64\mingw64\bin\g++.exe $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -E F:\Projects\DualKeyEncryption\src\dual_rsa_crypto.cpp > CMakeFiles\dual_rsa_crypto.dir\src\dual_rsa_crypto.cpp.i

CMakeFiles/dual_rsa_crypto.dir/src/dual_rsa_crypto.cpp.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Compiling CXX source to assembly CMakeFiles/dual_rsa_crypto.dir/src/dual_rsa_crypto.cpp.s"
	D:\msys64\mingw64\bin\g++.exe $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -S F:\Projects\DualKeyEncryption\src\dual_rsa_crypto.cpp -o CMakeFiles\dual_rsa_crypto.dir\src\dual_rsa_crypto.cpp.s

CMakeFiles/dual_rsa_crypto.dir/src/rsa_keys.cpp.obj: CMakeFiles/dual_rsa_crypto.dir/flags.make
CMakeFiles/dual_rsa_crypto.dir/src/rsa_keys.cpp.obj: CMakeFiles/dual_rsa_crypto.dir/includes_CXX.rsp
CMakeFiles/dual_rsa_crypto.dir/src/rsa_keys.cpp.obj: F:/Projects/DualKeyEncryption/src/rsa_keys.cpp
CMakeFiles/dual_rsa_crypto.dir/src/rsa_keys.cpp.obj: CMakeFiles/dual_rsa_crypto.dir/compiler_depend.ts
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green --progress-dir=F:\Projects\DualKeyEncryption\build\CMakeFiles --progress-num=$(CMAKE_PROGRESS_2) "Building CXX object CMakeFiles/dual_rsa_crypto.dir/src/rsa_keys.cpp.obj"
	D:\msys64\mingw64\bin\g++.exe $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -MD -MT CMakeFiles/dual_rsa_crypto.dir/src/rsa_keys.cpp.obj -MF CMakeFiles\dual_rsa_crypto.dir\src\rsa_keys.cpp.obj.d -o CMakeFiles\dual_rsa_crypto.dir\src\rsa_keys.cpp.obj -c F:\Projects\DualKeyEncryption\src\rsa_keys.cpp

CMakeFiles/dual_rsa_crypto.dir/src/rsa_keys.cpp.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Preprocessing CXX source to CMakeFiles/dual_rsa_crypto.dir/src/rsa_keys.cpp.i"
	D:\msys64\mingw64\bin\g++.exe $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -E F:\Projects\DualKeyEncryption\src\rsa_keys.cpp > CMakeFiles\dual_rsa_crypto.dir\src\rsa_keys.cpp.i

CMakeFiles/dual_rsa_crypto.dir/src/rsa_keys.cpp.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Compiling CXX source to assembly CMakeFiles/dual_rsa_crypto.dir/src/rsa_keys.cpp.s"
	D:\msys64\mingw64\bin\g++.exe $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -S F:\Projects\DualKeyEncryption\src\rsa_keys.cpp -o CMakeFiles\dual_rsa_crypto.dir\src\rsa_keys.cpp.s

CMakeFiles/dual_rsa_crypto.dir/src/crt_solver.cpp.obj: CMakeFiles/dual_rsa_crypto.dir/flags.make
CMakeFiles/dual_rsa_crypto.dir/src/crt_solver.cpp.obj: CMakeFiles/dual_rsa_crypto.dir/includes_CXX.rsp
CMakeFiles/dual_rsa_crypto.dir/src/crt_solver.cpp.obj: F:/Projects/DualKeyEncryption/src/crt_solver.cpp
CMakeFiles/dual_rsa_crypto.dir/src/crt_solver.cpp.obj: CMakeFiles/dual_rsa_crypto.dir/compiler_depend.ts
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green --progress-dir=F:\Projects\DualKeyEncryption\build\CMakeFiles --progress-num=$(CMAKE_PROGRESS_3) "Building CXX object CMakeFiles/dual_rsa_crypto.dir/src/crt_solver.cpp.obj"
	D:\msys64\mingw64\bin\g++.exe $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -MD -MT CMakeFiles/dual_rsa_crypto.dir/src/crt_solver.cpp.obj -MF CMakeFiles\dual_rsa_crypto.dir\src\crt_solver.cpp.obj.d -o CMakeFiles\dual_rsa_crypto.dir\src\crt_solver.cpp.obj -c F:\Projects\DualKeyEncryption\src\crt_solver.cpp

CMakeFiles/dual_rsa_crypto.dir/src/crt_solver.cpp.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Preprocessing CXX source to CMakeFiles/dual_rsa_crypto.dir/src/crt_solver.cpp.i"
	D:\msys64\mingw64\bin\g++.exe $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -E F:\Projects\DualKeyEncryption\src\crt_solver.cpp > CMakeFiles\dual_rsa_crypto.dir\src\crt_solver.cpp.i

CMakeFiles/dual_rsa_crypto.dir/src/crt_solver.cpp.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Compiling CXX source to assembly CMakeFiles/dual_rsa_crypto.dir/src/crt_solver.cpp.s"
	D:\msys64\mingw64\bin\g++.exe $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -S F:\Projects\DualKeyEncryption\src\crt_solver.cpp -o CMakeFiles\dual_rsa_crypto.dir\src\crt_solver.cpp.s

# Object files for target dual_rsa_crypto
dual_rsa_crypto_OBJECTS = \
"CMakeFiles/dual_rsa_crypto.dir/src/dual_rsa_crypto.cpp.obj" \
"CMakeFiles/dual_rsa_crypto.dir/src/rsa_keys.cpp.obj" \
"CMakeFiles/dual_rsa_crypto.dir/src/crt_solver.cpp.obj"

# External object files for target dual_rsa_crypto
dual_rsa_crypto_EXTERNAL_OBJECTS =

libdual_rsa_crypto.a: CMakeFiles/dual_rsa_crypto.dir/src/dual_rsa_crypto.cpp.obj
libdual_rsa_crypto.a: CMakeFiles/dual_rsa_crypto.dir/src/rsa_keys.cpp.obj
libdual_rsa_crypto.a: CMakeFiles/dual_rsa_crypto.dir/src/crt_solver.cpp.obj
libdual_rsa_crypto.a: CMakeFiles/dual_rsa_crypto.dir/build.make
libdual_rsa_crypto.a: CMakeFiles/dual_rsa_crypto.dir/link.txt
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green --bold --progress-dir=F:\Projects\DualKeyEncryption\build\CMakeFiles --progress-num=$(CMAKE_PROGRESS_4) "Linking CXX static library libdual_rsa_crypto.a"
	$(CMAKE_COMMAND) -P CMakeFiles\dual_rsa_crypto.dir\cmake_clean_target.cmake
	$(CMAKE_COMMAND) -E cmake_link_script CMakeFiles\dual_rsa_crypto.dir\link.txt --verbose=$(VERBOSE)

# Rule to build all files generated by this target.
CMakeFiles/dual_rsa_crypto.dir/build: libdual_rsa_crypto.a
.PHONY : CMakeFiles/dual_rsa_crypto.dir/build

CMakeFiles/dual_rsa_crypto.dir/clean:
	$(CMAKE_COMMAND) -P CMakeFiles\dual_rsa_crypto.dir\cmake_clean.cmake
.PHONY : CMakeFiles/dual_rsa_crypto.dir/clean

CMakeFiles/dual_rsa_crypto.dir/depend:
	$(CMAKE_COMMAND) -E cmake_depends "MinGW Makefiles" F:\Projects\DualKeyEncryption F:\Projects\DualKeyEncryption F:\Projects\DualKeyEncryption\build F:\Projects\DualKeyEncryption\build F:\Projects\DualKeyEncryption\build\CMakeFiles\dual_rsa_crypto.dir\DependInfo.cmake "--color=$(COLOR)"
.PHONY : CMakeFiles/dual_rsa_crypto.dir/depend

