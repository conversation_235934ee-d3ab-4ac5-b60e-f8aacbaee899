E:\cmake\bin\cmake.exe -E rm -f CMakeFiles\simple_test.dir/objects.a
D:\msys64\mingw64\bin\ar.exe qc CMakeFiles\simple_test.dir/objects.a @CMakeFiles\simple_test.dir\objects1.rsp
D:\msys64\mingw64\bin\g++.exe  -Wall -Wextra -Wpedantic -Wl,--whole-archive CMakeFiles\simple_test.dir/objects.a -Wl,--no-whole-archive -o simple_test.exe -Wl,--out-implib,libsimple_test.dll.a -Wl,--major-image-version,0,--minor-image-version,0 @CMakeFiles\simple_test.dir\linkLibs.rsp
