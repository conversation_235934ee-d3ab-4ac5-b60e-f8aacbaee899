#include "dual_rsa_crypto/rsa_keys.hpp"

#include <openssl/evp.h>
#include <openssl/rsa.h>
#include <openssl/pem.h>
#include <openssl/bio.h>
#include <openssl/bn.h>
#include <openssl/err.h>
#include <openssl/rand.h>
#include <openssl/core_names.h>
#include <openssl/param_build.h>

namespace dual_rsa_crypto {

namespace {
    // OpenSSL错误处理辅助函数
    std::string getOpenSSLError() {
        BIO* bio = BIO_new(BIO_s_mem());
        ERR_print_errors(bio);
        char* data;
        long len = BIO_get_mem_data(bio, &data);
        std::string result(data, len);
        BIO_free(bio);
        return result;
    }
    
    // BIGNUM转换为字节数组
    std::vector<uint8_t> bignumToBytes(const BIGNUM* bn) {
        if (!bn) {
            throw RSAKeyException("Invalid BIGNUM pointer");
        }
        
        int size = BN_num_bytes(bn);
        std::vector<uint8_t> result(size);
        BN_bn2bin(bn, result.data());
        return result;
    }
    
    // 字节数组转换为BIGNUM
    BIGNUM* bytesToBignum(const std::vector<uint8_t>& bytes) {
        return BN_bin2bn(bytes.data(), bytes.size(), nullptr);
    }
}

// RSAPublicKey::Impl 实现
class RSAPublicKey::Impl {
public:
    EVP_PKEY* pkey;
    
    Impl() : pkey(nullptr) {}
    
    ~Impl() {
        if (pkey) {
            EVP_PKEY_free(pkey);
        }
    }
    
    void generateKey(int key_size) {
        EVP_PKEY_CTX* ctx = EVP_PKEY_CTX_new_id(EVP_PKEY_RSA, nullptr);
        if (!ctx) {
            throw RSAKeyException("Failed to create EVP_PKEY_CTX: " + getOpenSSLError());
        }
        
        if (EVP_PKEY_keygen_init(ctx) <= 0) {
            EVP_PKEY_CTX_free(ctx);
            throw RSAKeyException("Failed to initialize key generation: " + getOpenSSLError());
        }
        
        if (EVP_PKEY_CTX_set_rsa_keygen_bits(ctx, key_size) <= 0) {
            EVP_PKEY_CTX_free(ctx);
            throw RSAKeyException("Failed to set key size: " + getOpenSSLError());
        }
        
        if (EVP_PKEY_keygen(ctx, &pkey) <= 0) {
            EVP_PKEY_CTX_free(ctx);
            throw RSAKeyException("Failed to generate key: " + getOpenSSLError());
        }
        
        EVP_PKEY_CTX_free(ctx);
    }
    
    void loadFromPEM(const std::string& pem_string) {
        BIO* bio = BIO_new_mem_buf(pem_string.c_str(), -1);
        if (!bio) {
            throw RSAKeyException("Failed to create BIO: " + getOpenSSLError());
        }
        
        pkey = PEM_read_bio_PUBKEY(bio, nullptr, nullptr, nullptr);
        BIO_free(bio);
        
        if (!pkey) {
            throw RSAKeyException("Failed to load public key from PEM: " + getOpenSSLError());
        }
        
        if (EVP_PKEY_base_id(pkey) != EVP_PKEY_RSA) {
            EVP_PKEY_free(pkey);
            pkey = nullptr;
            throw RSAKeyException("Key is not an RSA key");
        }
    }
};

// RSAPrivateKey::Impl 实现
class RSAPrivateKey::Impl {
public:
    EVP_PKEY* pkey;
    
    Impl() : pkey(nullptr) {}
    
    ~Impl() {
        if (pkey) {
            EVP_PKEY_free(pkey);
        }
    }
    
    void generateKey(int key_size) {
        EVP_PKEY_CTX* ctx = EVP_PKEY_CTX_new_id(EVP_PKEY_RSA, nullptr);
        if (!ctx) {
            throw RSAKeyException("Failed to create EVP_PKEY_CTX: " + getOpenSSLError());
        }
        
        if (EVP_PKEY_keygen_init(ctx) <= 0) {
            EVP_PKEY_CTX_free(ctx);
            throw RSAKeyException("Failed to initialize key generation: " + getOpenSSLError());
        }
        
        if (EVP_PKEY_CTX_set_rsa_keygen_bits(ctx, key_size) <= 0) {
            EVP_PKEY_CTX_free(ctx);
            throw RSAKeyException("Failed to set key size: " + getOpenSSLError());
        }
        
        if (EVP_PKEY_keygen(ctx, &pkey) <= 0) {
            EVP_PKEY_CTX_free(ctx);
            throw RSAKeyException("Failed to generate key: " + getOpenSSLError());
        }
        
        EVP_PKEY_CTX_free(ctx);
    }
    
    void loadFromPEM(const std::string& pem_string, const std::string& password) {
        BIO* bio = BIO_new_mem_buf(pem_string.c_str(), -1);
        if (!bio) {
            throw RSAKeyException("Failed to create BIO: " + getOpenSSLError());
        }
        
        const char* pass = password.empty() ? nullptr : password.c_str();
        pkey = PEM_read_bio_PrivateKey(bio, nullptr, nullptr, const_cast<char*>(pass));
        BIO_free(bio);
        
        if (!pkey) {
            throw RSAKeyException("Failed to load private key from PEM: " + getOpenSSLError());
        }
        
        if (EVP_PKEY_base_id(pkey) != EVP_PKEY_RSA) {
            EVP_PKEY_free(pkey);
            pkey = nullptr;
            throw RSAKeyException("Key is not an RSA key");
        }
    }
};

// RSAPublicKey 实现
RSAPublicKey::RSAPublicKey(int key_size) : pImpl(std::make_unique<Impl>()) {
    pImpl->generateKey(key_size);
}

RSAPublicKey::RSAPublicKey(const std::string& pem_string) : pImpl(std::make_unique<Impl>()) {
    pImpl->loadFromPEM(pem_string);
}

RSAPublicKey::RSAPublicKey(EVP_PKEY* pkey) : pImpl(std::make_unique<Impl>()) {
    if (!pkey) {
        throw RSAKeyException("Invalid EVP_PKEY pointer");
    }
    pImpl->pkey = pkey;
    EVP_PKEY_up_ref(pkey); // 增加引用计数
}

RSAPublicKey::RSAPublicKey(RSAPublicKey&& other) noexcept : pImpl(std::move(other.pImpl)) {}

RSAPublicKey& RSAPublicKey::operator=(RSAPublicKey&& other) noexcept {
    if (this != &other) {
        pImpl = std::move(other.pImpl);
    }
    return *this;
}

RSAPublicKey::~RSAPublicKey() = default;

int RSAPublicKey::getKeySize() const {
    return EVP_PKEY_bits(pImpl->pkey);
}

std::vector<uint8_t> RSAPublicKey::getModulus() const {
    BIGNUM* n = nullptr;
    if (EVP_PKEY_get_bn_param(pImpl->pkey, OSSL_PKEY_PARAM_RSA_N, &n) != 1) {
        throw RSAKeyException("Failed to get RSA modulus: " + getOpenSSLError());
    }

    std::vector<uint8_t> result = bignumToBytes(n);
    BN_free(n);
    return result;
}

std::vector<uint8_t> RSAPublicKey::getPublicExponent() const {
    BIGNUM* e = nullptr;
    if (EVP_PKEY_get_bn_param(pImpl->pkey, OSSL_PKEY_PARAM_RSA_E, &e) != 1) {
        throw RSAKeyException("Failed to get RSA public exponent: " + getOpenSSLError());
    }

    std::vector<uint8_t> result = bignumToBytes(e);
    BN_free(e);
    return result;
}

std::string RSAPublicKey::toPEM() const {
    BIO* bio = BIO_new(BIO_s_mem());
    if (!bio) {
        throw RSAKeyException("Failed to create BIO: " + getOpenSSLError());
    }
    
    if (PEM_write_bio_PUBKEY(bio, pImpl->pkey) != 1) {
        BIO_free(bio);
        throw RSAKeyException("Failed to write public key to PEM: " + getOpenSSLError());
    }
    
    char* data;
    long len = BIO_get_mem_data(bio, &data);
    std::string result(data, len);
    BIO_free(bio);
    
    return result;
}

EVP_PKEY* RSAPublicKey::getEVP_PKEY() const {
    return pImpl->pkey;
}

// RSAPrivateKey 实现
RSAPrivateKey::RSAPrivateKey(int key_size) : pImpl(std::make_unique<Impl>()) {
    pImpl->generateKey(key_size);
}

RSAPrivateKey::RSAPrivateKey(const std::string& pem_string, const std::string& password) 
    : pImpl(std::make_unique<Impl>()) {
    pImpl->loadFromPEM(pem_string, password);
}

RSAPrivateKey::RSAPrivateKey(EVP_PKEY* pkey) : pImpl(std::make_unique<Impl>()) {
    if (!pkey) {
        throw RSAKeyException("Invalid EVP_PKEY pointer");
    }
    pImpl->pkey = pkey;
    EVP_PKEY_up_ref(pkey); // 增加引用计数
}

RSAPrivateKey::RSAPrivateKey(RSAPrivateKey&& other) noexcept : pImpl(std::move(other.pImpl)) {}

RSAPrivateKey& RSAPrivateKey::operator=(RSAPrivateKey&& other) noexcept {
    if (this != &other) {
        pImpl = std::move(other.pImpl);
    }
    return *this;
}

RSAPrivateKey::~RSAPrivateKey() = default;

int RSAPrivateKey::getKeySize() const {
    return EVP_PKEY_bits(pImpl->pkey);
}

std::vector<uint8_t> RSAPrivateKey::getModulus() const {
    BIGNUM* n = nullptr;
    if (EVP_PKEY_get_bn_param(pImpl->pkey, OSSL_PKEY_PARAM_RSA_N, &n) != 1) {
        throw RSAKeyException("Failed to get RSA modulus: " + getOpenSSLError());
    }

    std::vector<uint8_t> result = bignumToBytes(n);
    BN_free(n);
    return result;
}

std::vector<uint8_t> RSAPrivateKey::getPrivateExponent() const {
    BIGNUM* d = nullptr;
    if (EVP_PKEY_get_bn_param(pImpl->pkey, OSSL_PKEY_PARAM_RSA_D, &d) != 1) {
        throw RSAKeyException("Failed to get RSA private exponent: " + getOpenSSLError());
    }

    std::vector<uint8_t> result = bignumToBytes(d);
    BN_free(d);
    return result;
}

RSAPublicKey RSAPrivateKey::getPublicKey() const {
    // 获取模数n和公钥指数e
    BIGNUM* n = nullptr;
    BIGNUM* e = nullptr;

    if (EVP_PKEY_get_bn_param(pImpl->pkey, OSSL_PKEY_PARAM_RSA_N, &n) != 1) {
        throw RSAKeyException("Failed to get RSA modulus: " + getOpenSSLError());
    }

    if (EVP_PKEY_get_bn_param(pImpl->pkey, OSSL_PKEY_PARAM_RSA_E, &e) != 1) {
        BN_free(n);
        throw RSAKeyException("Failed to get RSA public exponent: " + getOpenSSLError());
    }

    // 使用参数构建器创建公钥
    OSSL_PARAM_BLD* param_bld = OSSL_PARAM_BLD_new();
    if (!param_bld) {
        BN_free(n);
        BN_free(e);
        throw RSAKeyException("Failed to create parameter builder: " + getOpenSSLError());
    }

    if (OSSL_PARAM_BLD_push_BN(param_bld, OSSL_PKEY_PARAM_RSA_N, n) != 1 ||
        OSSL_PARAM_BLD_push_BN(param_bld, OSSL_PKEY_PARAM_RSA_E, e) != 1) {
        OSSL_PARAM_BLD_free(param_bld);
        BN_free(n);
        BN_free(e);
        throw RSAKeyException("Failed to push parameters: " + getOpenSSLError());
    }

    OSSL_PARAM* params = OSSL_PARAM_BLD_to_param(param_bld);
    if (!params) {
        OSSL_PARAM_BLD_free(param_bld);
        BN_free(n);
        BN_free(e);
        throw RSAKeyException("Failed to build parameters: " + getOpenSSLError());
    }

    // 创建公钥上下文
    EVP_PKEY_CTX* ctx = EVP_PKEY_CTX_new_from_name(nullptr, "RSA", nullptr);
    if (!ctx) {
        OSSL_PARAM_free(params);
        OSSL_PARAM_BLD_free(param_bld);
        BN_free(n);
        BN_free(e);
        throw RSAKeyException("Failed to create key context: " + getOpenSSLError());
    }

    if (EVP_PKEY_fromdata_init(ctx) != 1) {
        EVP_PKEY_CTX_free(ctx);
        OSSL_PARAM_free(params);
        OSSL_PARAM_BLD_free(param_bld);
        BN_free(n);
        BN_free(e);
        throw RSAKeyException("Failed to initialize fromdata: " + getOpenSSLError());
    }

    EVP_PKEY* pub_pkey = nullptr;
    if (EVP_PKEY_fromdata(ctx, &pub_pkey, EVP_PKEY_PUBLIC_KEY, params) != 1) {
        EVP_PKEY_CTX_free(ctx);
        OSSL_PARAM_free(params);
        OSSL_PARAM_BLD_free(param_bld);
        BN_free(n);
        BN_free(e);
        throw RSAKeyException("Failed to create public key from data: " + getOpenSSLError());
    }

    // 清理资源
    EVP_PKEY_CTX_free(ctx);
    OSSL_PARAM_free(params);
    OSSL_PARAM_BLD_free(param_bld);
    BN_free(n);
    BN_free(e);

    return RSAPublicKey(pub_pkey);
}

std::string RSAPrivateKey::toPEM(const std::string& password) const {
    BIO* bio = BIO_new(BIO_s_mem());
    if (!bio) {
        throw RSAKeyException("Failed to create BIO: " + getOpenSSLError());
    }

    int result;
    if (password.empty()) {
        result = PEM_write_bio_PrivateKey(bio, pImpl->pkey, nullptr, nullptr, 0, nullptr, nullptr);
    } else {
        result = PEM_write_bio_PrivateKey(bio, pImpl->pkey, EVP_aes_256_cbc(),
                                        const_cast<unsigned char*>(reinterpret_cast<const unsigned char*>(password.c_str())),
                                        password.length(), nullptr, nullptr);
    }

    if (result != 1) {
        BIO_free(bio);
        throw RSAKeyException("Failed to write private key to PEM: " + getOpenSSLError());
    }

    char* data;
    long len = BIO_get_mem_data(bio, &data);
    std::string result_str(data, len);
    BIO_free(bio);

    return result_str;
}

EVP_PKEY* RSAPrivateKey::getEVP_PKEY() const {
    return pImpl->pkey;
}

// 辅助函数实现
std::pair<RSAPrivateKey, RSAPublicKey> generateRSAKeyPair(int key_size) {
    RSAPrivateKey private_key(key_size);
    RSAPublicKey public_key = private_key.getPublicKey();
    return std::make_pair(std::move(private_key), std::move(public_key));
}

bool areModuliCoprime(const std::vector<uint8_t>& modulus1, const std::vector<uint8_t>& modulus2) {
    BIGNUM* n1 = bytesToBignum(modulus1);
    BIGNUM* n2 = bytesToBignum(modulus2);
    BIGNUM* gcd = BN_new();
    BN_CTX* ctx = BN_CTX_new();

    if (!n1 || !n2 || !gcd || !ctx) {
        if (n1) BN_free(n1);
        if (n2) BN_free(n2);
        if (gcd) BN_free(gcd);
        if (ctx) BN_CTX_free(ctx);
        throw RSAKeyException("Failed to create BIGNUM structures");
    }

    if (BN_gcd(gcd, n1, n2, ctx) != 1) {
        BN_free(n1);
        BN_free(n2);
        BN_free(gcd);
        BN_CTX_free(ctx);
        throw RSAKeyException("Failed to compute GCD");
    }

    bool result = BN_is_one(gcd);

    BN_free(n1);
    BN_free(n2);
    BN_free(gcd);
    BN_CTX_free(ctx);

    return result;
}

} // namespace dual_rsa_crypto
