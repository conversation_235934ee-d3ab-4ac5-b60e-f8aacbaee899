#include "dual_rsa_crypto/rsa_keys.hpp"

#include <openssl/evp.h>
#include <openssl/rsa.h>
#include <openssl/pem.h>
#include <openssl/bio.h>
#include <openssl/bn.h>
#include <openssl/err.h>
#include <openssl/rand.h>

namespace dual_rsa_crypto {

namespace {
    // OpenSSL错误处理辅助函数
    std::string getOpenSSLError() {
        BIO* bio = BIO_new(BIO_s_mem());
        ERR_print_errors(bio);
        char* data;
        long len = BIO_get_mem_data(bio, &data);
        std::string result(data, len);
        BIO_free(bio);
        return result;
    }
    
    // BIGNUM转换为字节数组
    std::vector<uint8_t> bignumToBytes(const BIGNUM* bn) {
        if (!bn) {
            throw RSAKeyException("Invalid BIGNUM pointer");
        }
        
        int size = BN_num_bytes(bn);
        std::vector<uint8_t> result(size);
        BN_bn2bin(bn, result.data());
        return result;
    }
    
    // 字节数组转换为BIGNUM
    BIGNUM* bytesToBignum(const std::vector<uint8_t>& bytes) {
        return BN_bin2bn(bytes.data(), bytes.size(), nullptr);
    }
}

// RSAPublicKey::Impl 实现
class RSAPublicKey::Impl {
public:
    EVP_PKEY* pkey;
    
    Impl() : pkey(nullptr) {}
    
    ~Impl() {
        if (pkey) {
            EVP_PKEY_free(pkey);
        }
    }
    
    void generateKey(int key_size) {
        EVP_PKEY_CTX* ctx = EVP_PKEY_CTX_new_id(EVP_PKEY_RSA, nullptr);
        if (!ctx) {
            throw RSAKeyException("Failed to create EVP_PKEY_CTX: " + getOpenSSLError());
        }
        
        if (EVP_PKEY_keygen_init(ctx) <= 0) {
            EVP_PKEY_CTX_free(ctx);
            throw RSAKeyException("Failed to initialize key generation: " + getOpenSSLError());
        }
        
        if (EVP_PKEY_CTX_set_rsa_keygen_bits(ctx, key_size) <= 0) {
            EVP_PKEY_CTX_free(ctx);
            throw RSAKeyException("Failed to set key size: " + getOpenSSLError());
        }
        
        if (EVP_PKEY_keygen(ctx, &pkey) <= 0) {
            EVP_PKEY_CTX_free(ctx);
            throw RSAKeyException("Failed to generate key: " + getOpenSSLError());
        }
        
        EVP_PKEY_CTX_free(ctx);
    }
    
    void loadFromPEM(const std::string& pem_string) {
        BIO* bio = BIO_new_mem_buf(pem_string.c_str(), -1);
        if (!bio) {
            throw RSAKeyException("Failed to create BIO: " + getOpenSSLError());
        }
        
        pkey = PEM_read_bio_PUBKEY(bio, nullptr, nullptr, nullptr);
        BIO_free(bio);
        
        if (!pkey) {
            throw RSAKeyException("Failed to load public key from PEM: " + getOpenSSLError());
        }
        
        if (EVP_PKEY_base_id(pkey) != EVP_PKEY_RSA) {
            EVP_PKEY_free(pkey);
            pkey = nullptr;
            throw RSAKeyException("Key is not an RSA key");
        }
    }
};

// RSAPrivateKey::Impl 实现
class RSAPrivateKey::Impl {
public:
    EVP_PKEY* pkey;
    
    Impl() : pkey(nullptr) {}
    
    ~Impl() {
        if (pkey) {
            EVP_PKEY_free(pkey);
        }
    }
    
    void generateKey(int key_size) {
        EVP_PKEY_CTX* ctx = EVP_PKEY_CTX_new_id(EVP_PKEY_RSA, nullptr);
        if (!ctx) {
            throw RSAKeyException("Failed to create EVP_PKEY_CTX: " + getOpenSSLError());
        }
        
        if (EVP_PKEY_keygen_init(ctx) <= 0) {
            EVP_PKEY_CTX_free(ctx);
            throw RSAKeyException("Failed to initialize key generation: " + getOpenSSLError());
        }
        
        if (EVP_PKEY_CTX_set_rsa_keygen_bits(ctx, key_size) <= 0) {
            EVP_PKEY_CTX_free(ctx);
            throw RSAKeyException("Failed to set key size: " + getOpenSSLError());
        }
        
        if (EVP_PKEY_keygen(ctx, &pkey) <= 0) {
            EVP_PKEY_CTX_free(ctx);
            throw RSAKeyException("Failed to generate key: " + getOpenSSLError());
        }
        
        EVP_PKEY_CTX_free(ctx);
    }
    
    void loadFromPEM(const std::string& pem_string, const std::string& password) {
        BIO* bio = BIO_new_mem_buf(pem_string.c_str(), -1);
        if (!bio) {
            throw RSAKeyException("Failed to create BIO: " + getOpenSSLError());
        }
        
        const char* pass = password.empty() ? nullptr : password.c_str();
        pkey = PEM_read_bio_PrivateKey(bio, nullptr, nullptr, const_cast<char*>(pass));
        BIO_free(bio);
        
        if (!pkey) {
            throw RSAKeyException("Failed to load private key from PEM: " + getOpenSSLError());
        }
        
        if (EVP_PKEY_base_id(pkey) != EVP_PKEY_RSA) {
            EVP_PKEY_free(pkey);
            pkey = nullptr;
            throw RSAKeyException("Key is not an RSA key");
        }
    }
};

// RSAPublicKey 实现
RSAPublicKey::RSAPublicKey(int key_size) : pImpl(std::make_unique<Impl>()) {
    pImpl->generateKey(key_size);
}

RSAPublicKey::RSAPublicKey(const std::string& pem_string) : pImpl(std::make_unique<Impl>()) {
    pImpl->loadFromPEM(pem_string);
}

RSAPublicKey::RSAPublicKey(EVP_PKEY* pkey) : pImpl(std::make_unique<Impl>()) {
    if (!pkey) {
        throw RSAKeyException("Invalid EVP_PKEY pointer");
    }
    pImpl->pkey = pkey;
    EVP_PKEY_up_ref(pkey); // 增加引用计数
}

RSAPublicKey::RSAPublicKey(RSAPublicKey&& other) noexcept : pImpl(std::move(other.pImpl)) {}

RSAPublicKey& RSAPublicKey::operator=(RSAPublicKey&& other) noexcept {
    if (this != &other) {
        pImpl = std::move(other.pImpl);
    }
    return *this;
}

RSAPublicKey::~RSAPublicKey() = default;

int RSAPublicKey::getKeySize() const {
    return EVP_PKEY_bits(pImpl->pkey);
}

std::vector<uint8_t> RSAPublicKey::getModulus() const {
    const RSA* rsa = EVP_PKEY_get0_RSA(pImpl->pkey);
    if (!rsa) {
        throw RSAKeyException("Failed to get RSA structure");
    }
    
    const BIGNUM* n;
    RSA_get0_key(rsa, &n, nullptr, nullptr);
    return bignumToBytes(n);
}

std::vector<uint8_t> RSAPublicKey::getPublicExponent() const {
    const RSA* rsa = EVP_PKEY_get0_RSA(pImpl->pkey);
    if (!rsa) {
        throw RSAKeyException("Failed to get RSA structure");
    }
    
    const BIGNUM* e;
    RSA_get0_key(rsa, nullptr, &e, nullptr);
    return bignumToBytes(e);
}

std::string RSAPublicKey::toPEM() const {
    BIO* bio = BIO_new(BIO_s_mem());
    if (!bio) {
        throw RSAKeyException("Failed to create BIO: " + getOpenSSLError());
    }
    
    if (PEM_write_bio_PUBKEY(bio, pImpl->pkey) != 1) {
        BIO_free(bio);
        throw RSAKeyException("Failed to write public key to PEM: " + getOpenSSLError());
    }
    
    char* data;
    long len = BIO_get_mem_data(bio, &data);
    std::string result(data, len);
    BIO_free(bio);
    
    return result;
}

EVP_PKEY* RSAPublicKey::getEVP_PKEY() const {
    return pImpl->pkey;
}

// RSAPrivateKey 实现
RSAPrivateKey::RSAPrivateKey(int key_size) : pImpl(std::make_unique<Impl>()) {
    pImpl->generateKey(key_size);
}

RSAPrivateKey::RSAPrivateKey(const std::string& pem_string, const std::string& password) 
    : pImpl(std::make_unique<Impl>()) {
    pImpl->loadFromPEM(pem_string, password);
}

RSAPrivateKey::RSAPrivateKey(EVP_PKEY* pkey) : pImpl(std::make_unique<Impl>()) {
    if (!pkey) {
        throw RSAKeyException("Invalid EVP_PKEY pointer");
    }
    pImpl->pkey = pkey;
    EVP_PKEY_up_ref(pkey); // 增加引用计数
}

RSAPrivateKey::RSAPrivateKey(RSAPrivateKey&& other) noexcept : pImpl(std::move(other.pImpl)) {}

RSAPrivateKey& RSAPrivateKey::operator=(RSAPrivateKey&& other) noexcept {
    if (this != &other) {
        pImpl = std::move(other.pImpl);
    }
    return *this;
}

RSAPrivateKey::~RSAPrivateKey() = default;

int RSAPrivateKey::getKeySize() const {
    return EVP_PKEY_bits(pImpl->pkey);
}

std::vector<uint8_t> RSAPrivateKey::getModulus() const {
    const RSA* rsa = EVP_PKEY_get0_RSA(pImpl->pkey);
    if (!rsa) {
        throw RSAKeyException("Failed to get RSA structure");
    }

    const BIGNUM* n;
    RSA_get0_key(rsa, &n, nullptr, nullptr);
    return bignumToBytes(n);
}

std::vector<uint8_t> RSAPrivateKey::getPrivateExponent() const {
    const RSA* rsa = EVP_PKEY_get0_RSA(pImpl->pkey);
    if (!rsa) {
        throw RSAKeyException("Failed to get RSA structure");
    }

    const BIGNUM* d;
    RSA_get0_key(rsa, nullptr, nullptr, &d);
    return bignumToBytes(d);
}

RSAPublicKey RSAPrivateKey::getPublicKey() const {
    // 创建新的EVP_PKEY只包含公钥部分
    EVP_PKEY* pub_pkey = EVP_PKEY_new();
    if (!pub_pkey) {
        throw RSAKeyException("Failed to create new EVP_PKEY: " + getOpenSSLError());
    }

    const RSA* rsa = EVP_PKEY_get0_RSA(pImpl->pkey);
    if (!rsa) {
        EVP_PKEY_free(pub_pkey);
        throw RSAKeyException("Failed to get RSA structure");
    }

    // 创建新的RSA结构只包含公钥
    RSA* pub_rsa = RSA_new();
    if (!pub_rsa) {
        EVP_PKEY_free(pub_pkey);
        throw RSAKeyException("Failed to create new RSA structure: " + getOpenSSLError());
    }

    const BIGNUM* n, *e;
    RSA_get0_key(rsa, &n, &e, nullptr);

    BIGNUM* n_copy = BN_dup(n);
    BIGNUM* e_copy = BN_dup(e);

    if (!n_copy || !e_copy) {
        RSA_free(pub_rsa);
        EVP_PKEY_free(pub_pkey);
        if (n_copy) BN_free(n_copy);
        if (e_copy) BN_free(e_copy);
        throw RSAKeyException("Failed to duplicate BIGNUM: " + getOpenSSLError());
    }

    if (RSA_set0_key(pub_rsa, n_copy, e_copy, nullptr) != 1) {
        RSA_free(pub_rsa);
        EVP_PKEY_free(pub_pkey);
        BN_free(n_copy);
        BN_free(e_copy);
        throw RSAKeyException("Failed to set RSA key: " + getOpenSSLError());
    }

    if (EVP_PKEY_set1_RSA(pub_pkey, pub_rsa) != 1) {
        RSA_free(pub_rsa);
        EVP_PKEY_free(pub_pkey);
        throw RSAKeyException("Failed to set EVP_PKEY: " + getOpenSSLError());
    }

    RSA_free(pub_rsa); // EVP_PKEY_set1_RSA增加了引用计数

    return RSAPublicKey(pub_pkey);
}

std::string RSAPrivateKey::toPEM(const std::string& password) const {
    BIO* bio = BIO_new(BIO_s_mem());
    if (!bio) {
        throw RSAKeyException("Failed to create BIO: " + getOpenSSLError());
    }

    int result;
    if (password.empty()) {
        result = PEM_write_bio_PrivateKey(bio, pImpl->pkey, nullptr, nullptr, 0, nullptr, nullptr);
    } else {
        result = PEM_write_bio_PrivateKey(bio, pImpl->pkey, EVP_aes_256_cbc(),
                                        const_cast<unsigned char*>(reinterpret_cast<const unsigned char*>(password.c_str())),
                                        password.length(), nullptr, nullptr);
    }

    if (result != 1) {
        BIO_free(bio);
        throw RSAKeyException("Failed to write private key to PEM: " + getOpenSSLError());
    }

    char* data;
    long len = BIO_get_mem_data(bio, &data);
    std::string result_str(data, len);
    BIO_free(bio);

    return result_str;
}

EVP_PKEY* RSAPrivateKey::getEVP_PKEY() const {
    return pImpl->pkey;
}

// 辅助函数实现
std::pair<RSAPrivateKey, RSAPublicKey> generateRSAKeyPair(int key_size) {
    RSAPrivateKey private_key(key_size);
    RSAPublicKey public_key = private_key.getPublicKey();
    return std::make_pair(std::move(private_key), std::move(public_key));
}

bool areModuliCoprime(const std::vector<uint8_t>& modulus1, const std::vector<uint8_t>& modulus2) {
    BIGNUM* n1 = bytesToBignum(modulus1);
    BIGNUM* n2 = bytesToBignum(modulus2);
    BIGNUM* gcd = BN_new();
    BN_CTX* ctx = BN_CTX_new();

    if (!n1 || !n2 || !gcd || !ctx) {
        if (n1) BN_free(n1);
        if (n2) BN_free(n2);
        if (gcd) BN_free(gcd);
        if (ctx) BN_CTX_free(ctx);
        throw RSAKeyException("Failed to create BIGNUM structures");
    }

    if (BN_gcd(gcd, n1, n2, ctx) != 1) {
        BN_free(n1);
        BN_free(n2);
        BN_free(gcd);
        BN_CTX_free(ctx);
        throw RSAKeyException("Failed to compute GCD");
    }

    bool result = BN_is_one(gcd);

    BN_free(n1);
    BN_free(n2);
    BN_free(gcd);
    BN_CTX_free(ctx);

    return result;
}

} // namespace dual_rsa_crypto
