#include <openssl/evp.h>
#include <openssl/rsa.h>
#include <openssl/rand.h>
#include <openssl/err.h>
#include <iostream>

int main() {
    std::cout << "Starting debug test..." << std::endl;

    // Initialize OpenSSL
    std::cout << "Initializing OpenSSL..." << std::endl;

    // Check random number generator
    std::cout << "Checking random number generator..." << std::endl;
    if (RAND_status() != 1) {
        std::cout << "Random number generator not properly initialized" << std::endl;
        return 1;
    }
    std::cout << "Random number generator OK" << std::endl;

    // Create EVP_PKEY_CTX
    std::cout << "Creating EVP_PKEY_CTX..." << std::endl;
    EVP_PKEY_CTX* ctx = EVP_PKEY_CTX_new_id(EVP_PKEY_RSA, nullptr);
    if (!ctx) {
        std::cout << "Failed to create EVP_PKEY_CTX" << std::endl;
        return 1;
    }
    std::cout << "EVP_PKEY_CTX created successfully" << std::endl;

    // Initialize key generation
    std::cout << "Initializing key generation..." << std::endl;
    if (EVP_PKEY_keygen_init(ctx) <= 0) {
        std::cout << "Failed to initialize key generation" << std::endl;
        EVP_PKEY_CTX_free(ctx);
        return 1;
    }
    std::cout << "Key generation initialized successfully" << std::endl;

    // Set key size
    std::cout << "Setting key size to 1024 bits..." << std::endl;
    if (EVP_PKEY_CTX_set_rsa_keygen_bits(ctx, 1024) <= 0) {
        std::cout << "Failed to set key size" << std::endl;
        EVP_PKEY_CTX_free(ctx);
        return 1;
    }
    std::cout << "Key size set successfully" << std::endl;

    // Generate key
    std::cout << "Starting key generation..." << std::endl;
    EVP_PKEY* pkey = nullptr;
    if (EVP_PKEY_keygen(ctx, &pkey) <= 0) {
        std::cout << "Key generation failed" << std::endl;
        EVP_PKEY_CTX_free(ctx);
        return 1;
    }
    std::cout << "Key generation successful!" << std::endl;

    // Cleanup
    EVP_PKEY_free(pkey);
    EVP_PKEY_CTX_free(ctx);

    std::cout << "Debug test completed!" << std::endl;
    return 0;
}
