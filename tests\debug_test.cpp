#include <openssl/evp.h>
#include <openssl/rsa.h>
#include <openssl/rand.h>
#include <openssl/err.h>
#include <iostream>

int main() {
    std::cout << "开始调试测试..." << std::endl;
    
    // 初始化OpenSSL
    std::cout << "初始化OpenSSL..." << std::endl;
    
    // 检查随机数生成器
    std::cout << "检查随机数生成器..." << std::endl;
    if (RAND_status() != 1) {
        std::cout << "随机数生成器未正确初始化" << std::endl;
        return 1;
    }
    std::cout << "随机数生成器正常" << std::endl;
    
    // 创建EVP_PKEY_CTX
    std::cout << "创建EVP_PKEY_CTX..." << std::endl;
    EVP_PKEY_CTX* ctx = EVP_PKEY_CTX_new_id(EVP_PKEY_RSA, nullptr);
    if (!ctx) {
        std::cout << "创建EVP_PKEY_CTX失败" << std::endl;
        return 1;
    }
    std::cout << "EVP_PKEY_CTX创建成功" << std::endl;
    
    // 初始化密钥生成
    std::cout << "初始化密钥生成..." << std::endl;
    if (EVP_PKEY_keygen_init(ctx) <= 0) {
        std::cout << "初始化密钥生成失败" << std::endl;
        EVP_PKEY_CTX_free(ctx);
        return 1;
    }
    std::cout << "密钥生成初始化成功" << std::endl;
    
    // 设置密钥大小
    std::cout << "设置密钥大小为1024位..." << std::endl;
    if (EVP_PKEY_CTX_set_rsa_keygen_bits(ctx, 1024) <= 0) {
        std::cout << "设置密钥大小失败" << std::endl;
        EVP_PKEY_CTX_free(ctx);
        return 1;
    }
    std::cout << "密钥大小设置成功" << std::endl;
    
    // 生成密钥
    std::cout << "开始生成密钥..." << std::endl;
    EVP_PKEY* pkey = nullptr;
    if (EVP_PKEY_keygen(ctx, &pkey) <= 0) {
        std::cout << "密钥生成失败" << std::endl;
        EVP_PKEY_CTX_free(ctx);
        return 1;
    }
    std::cout << "密钥生成成功!" << std::endl;
    
    // 清理
    EVP_PKEY_free(pkey);
    EVP_PKEY_CTX_free(ctx);
    
    std::cout << "调试测试完成!" << std::endl;
    return 0;
}
