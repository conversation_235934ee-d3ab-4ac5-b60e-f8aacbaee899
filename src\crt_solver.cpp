#include "dual_rsa_crypto/rsa_keys.hpp"

#include <openssl/bn.h>
#include <openssl/err.h>
#include <stdexcept>
#include <vector>

namespace dual_rsa_crypto {

namespace {
    // OpenSSL错误处理辅助函数
    std::string getOpenSSLError() {
        BIO* bio = BIO_new(BIO_s_mem());
        ERR_print_errors(bio);
        char* data;
        long len = BIO_get_mem_data(bio, &data);
        std::string result(data, len);
        BIO_free(bio);
        return result;
    }
    
    // BIGNUM转换为字节数组
    std::vector<uint8_t> bignumToBytes(const BIGNUM* bn) {
        if (!bn) {
            throw std::runtime_error("Invalid BIGNUM pointer");
        }
        
        int size = BN_num_bytes(bn);
        std::vector<uint8_t> result(size);
        BN_bn2bin(bn, result.data());
        return result;
    }
    
    // 字节数组转换为BIGNUM
    BIGNUM* bytesToBignum(const std::vector<uint8_t>& bytes) {
        return BN_bin2bn(bytes.data(), bytes.size(), nullptr);
    }
    
    // RAII包装器用于BIGNUM
    class BignumWrapper {
    public:
        BIGNUM* bn;
        
        BignumWrapper() : bn(BN_new()) {
            if (!bn) {
                throw std::runtime_error("Failed to create BIGNUM");
            }
        }
        
        explicit BignumWrapper(BIGNUM* b) : bn(b) {}
        
        ~BignumWrapper() {
            if (bn) {
                BN_free(bn);
            }
        }
        
        // 禁用拷贝
        BignumWrapper(const BignumWrapper&) = delete;
        BignumWrapper& operator=(const BignumWrapper&) = delete;
        
        // 启用移动
        BignumWrapper(BignumWrapper&& other) noexcept : bn(other.bn) {
            other.bn = nullptr;
        }
        
        BignumWrapper& operator=(BignumWrapper&& other) noexcept {
            if (this != &other) {
                if (bn) BN_free(bn);
                bn = other.bn;
                other.bn = nullptr;
            }
            return *this;
        }
        
        operator BIGNUM*() const { return bn; }
        BIGNUM* get() const { return bn; }
        BIGNUM* release() {
            BIGNUM* temp = bn;
            bn = nullptr;
            return temp;
        }
    };
    
    // RAII包装器用于BN_CTX
    class BnCtxWrapper {
    public:
        BN_CTX* ctx;
        
        BnCtxWrapper() : ctx(BN_CTX_new()) {
            if (!ctx) {
                throw std::runtime_error("Failed to create BN_CTX");
            }
        }
        
        ~BnCtxWrapper() {
            if (ctx) {
                BN_CTX_free(ctx);
            }
        }
        
        // 禁用拷贝和移动
        BnCtxWrapper(const BnCtxWrapper&) = delete;
        BnCtxWrapper& operator=(const BnCtxWrapper&) = delete;
        BnCtxWrapper(BnCtxWrapper&&) = delete;
        BnCtxWrapper& operator=(BnCtxWrapper&&) = delete;
        
        operator BN_CTX*() const { return ctx; }
        BN_CTX* get() const { return ctx; }
    };
}

/**
 * @brief 中国剩余定理求解器
 * 
 * 求解同余方程组：
 * x ≡ a1 (mod n1)
 * x ≡ a2 (mod n2)
 * 
 * 其中 gcd(n1, n2) = 1
 */
class CRTSolver {
public:
    /**
     * @brief 求解两个同余方程的中国剩余定理
     * @param a1 第一个余数
     * @param n1 第一个模数
     * @param a2 第二个余数
     * @param n2 第二个模数
     * @return 解x，满足 0 <= x < n1*n2
     */
    static std::vector<uint8_t> solve(const std::vector<uint8_t>& a1, 
                                     const std::vector<uint8_t>& n1,
                                     const std::vector<uint8_t>& a2, 
                                     const std::vector<uint8_t>& n2) {
        
        BignumWrapper bn_a1(bytesToBignum(a1));
        BignumWrapper bn_n1(bytesToBignum(n1));
        BignumWrapper bn_a2(bytesToBignum(a2));
        BignumWrapper bn_n2(bytesToBignum(n2));
        BnCtxWrapper ctx;
        
        // 验证模数互质
        BignumWrapper gcd;
        if (BN_gcd(gcd, bn_n1, bn_n2, ctx) != 1) {
            throw std::runtime_error("Failed to compute GCD: " + getOpenSSLError());
        }
        
        if (!BN_is_one(gcd)) {
            throw std::runtime_error("Moduli are not coprime");
        }
        
        // 计算 N = n1 * n2
        BignumWrapper N;
        if (BN_mul(N, bn_n1, bn_n2, ctx) != 1) {
            throw std::runtime_error("Failed to compute N = n1 * n2: " + getOpenSSLError());
        }
        
        // 计算 M1 = N / n1 = n2
        BignumWrapper M1;
        if (BN_copy(M1, bn_n2) == nullptr) {
            throw std::runtime_error("Failed to copy n2 to M1: " + getOpenSSLError());
        }
        
        // 计算 M2 = N / n2 = n1
        BignumWrapper M2;
        if (BN_copy(M2, bn_n1) == nullptr) {
            throw std::runtime_error("Failed to copy n1 to M2: " + getOpenSSLError());
        }
        
        // 计算 y1，使得 M1 * y1 ≡ 1 (mod n1)
        // 即 n2 * y1 ≡ 1 (mod n1)
        BignumWrapper y1;
        if (BN_mod_inverse(y1, M1, bn_n1, ctx) == nullptr) {
            throw std::runtime_error("Failed to compute modular inverse y1: " + getOpenSSLError());
        }
        
        // 计算 y2，使得 M2 * y2 ≡ 1 (mod n2)
        // 即 n1 * y2 ≡ 1 (mod n2)
        BignumWrapper y2;
        if (BN_mod_inverse(y2, M2, bn_n2, ctx) == nullptr) {
            throw std::runtime_error("Failed to compute modular inverse y2: " + getOpenSSLError());
        }
        
        // 计算 x = (a1 * M1 * y1 + a2 * M2 * y2) mod N
        BignumWrapper term1, term2, temp1, temp2;
        
        // term1 = a1 * M1 * y1
        if (BN_mul(temp1, bn_a1, M1, ctx) != 1) {
            throw std::runtime_error("Failed to compute a1 * M1: " + getOpenSSLError());
        }
        if (BN_mul(term1, temp1, y1, ctx) != 1) {
            throw std::runtime_error("Failed to compute a1 * M1 * y1: " + getOpenSSLError());
        }
        
        // term2 = a2 * M2 * y2
        if (BN_mul(temp2, bn_a2, M2, ctx) != 1) {
            throw std::runtime_error("Failed to compute a2 * M2: " + getOpenSSLError());
        }
        if (BN_mul(term2, temp2, y2, ctx) != 1) {
            throw std::runtime_error("Failed to compute a2 * M2 * y2: " + getOpenSSLError());
        }
        
        // x = (term1 + term2) mod N
        BignumWrapper x, sum;
        if (BN_add(sum, term1, term2) != 1) {
            throw std::runtime_error("Failed to add terms: " + getOpenSSLError());
        }
        if (BN_mod(x, sum, N, ctx) != 1) {
            throw std::runtime_error("Failed to compute final modulo: " + getOpenSSLError());
        }
        
        return bignumToBytes(x);
    }
    
    /**
     * @brief 验证CRT解的正确性
     * @param x CRT解
     * @param a1 第一个余数
     * @param n1 第一个模数
     * @param a2 第二个余数
     * @param n2 第二个模数
     * @return 如果解正确返回true
     */
    static bool verify(const std::vector<uint8_t>& x,
                      const std::vector<uint8_t>& a1, 
                      const std::vector<uint8_t>& n1,
                      const std::vector<uint8_t>& a2, 
                      const std::vector<uint8_t>& n2) {
        
        BignumWrapper bn_x(bytesToBignum(x));
        BignumWrapper bn_a1(bytesToBignum(a1));
        BignumWrapper bn_n1(bytesToBignum(n1));
        BignumWrapper bn_a2(bytesToBignum(a2));
        BignumWrapper bn_n2(bytesToBignum(n2));
        BnCtxWrapper ctx;
        
        // 验证 x ≡ a1 (mod n1)
        BignumWrapper remainder1;
        if (BN_mod(remainder1, bn_x, bn_n1, ctx) != 1) {
            return false;
        }
        if (BN_cmp(remainder1, bn_a1) != 0) {
            return false;
        }
        
        // 验证 x ≡ a2 (mod n2)
        BignumWrapper remainder2;
        if (BN_mod(remainder2, bn_x, bn_n2, ctx) != 1) {
            return false;
        }
        if (BN_cmp(remainder2, bn_a2) != 0) {
            return false;
        }
        
        return true;
    }
};

// 导出CRT求解函数供外部使用
std::vector<uint8_t> solveCRT(const std::vector<uint8_t>& a1, 
                             const std::vector<uint8_t>& n1,
                             const std::vector<uint8_t>& a2, 
                             const std::vector<uint8_t>& n2) {
    return CRTSolver::solve(a1, n1, a2, n2);
}

bool verifyCRT(const std::vector<uint8_t>& x,
               const std::vector<uint8_t>& a1, 
               const std::vector<uint8_t>& n1,
               const std::vector<uint8_t>& a2, 
               const std::vector<uint8_t>& n2) {
    return CRTSolver::verify(x, a1, n1, a2, n2);
}

} // namespace dual_rsa_crypto
