E:\cmake\bin\cmake.exe -E rm -f CMakeFiles\core_test.dir/objects.a
D:\msys64\mingw64\bin\ar.exe qc CMakeFiles\core_test.dir/objects.a @CMakeFiles\core_test.dir\objects1.rsp
D:\msys64\mingw64\bin\g++.exe  -Wall -Wextra -Wpedantic -g -O0 -Wl,--whole-archive CMakeFiles\core_test.dir/objects.a -Wl,--no-whole-archive -o core_test.exe -Wl,--out-implib,libcore_test.dll.a -Wl,--major-image-version,0,--minor-image-version,0 @CMakeFiles\core_test.dir\linkLibs.rsp
