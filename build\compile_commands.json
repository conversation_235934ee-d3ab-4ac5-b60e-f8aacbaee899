[{"directory": "F:/Projects/DualKeyEncryption/build", "command": "D:\\msys64\\mingw64\\bin\\g++.exe  @CMakeFiles/dual_rsa_crypto.dir/includes_CXX.rsp  -Wall -Wextra -Wpedantic -g -O0 -std=c++17 -o CMakeFiles\\dual_rsa_crypto.dir\\src\\dual_rsa_crypto.cpp.obj -c F:\\Projects\\DualKeyEncryption\\src\\dual_rsa_crypto.cpp", "file": "F:/Projects/DualKeyEncryption/src/dual_rsa_crypto.cpp", "output": "CMakeFiles/dual_rsa_crypto.dir/src/dual_rsa_crypto.cpp.obj"}, {"directory": "F:/Projects/DualKeyEncryption/build", "command": "D:\\msys64\\mingw64\\bin\\g++.exe  @CMakeFiles/dual_rsa_crypto.dir/includes_CXX.rsp  -Wall -Wextra -Wpedantic -g -O0 -std=c++17 -o CMakeFiles\\dual_rsa_crypto.dir\\src\\rsa_keys.cpp.obj -c F:\\Projects\\DualKeyEncryption\\src\\rsa_keys.cpp", "file": "F:/Projects/DualKeyEncryption/src/rsa_keys.cpp", "output": "CMakeFiles/dual_rsa_crypto.dir/src/rsa_keys.cpp.obj"}, {"directory": "F:/Projects/DualKeyEncryption/build", "command": "D:\\msys64\\mingw64\\bin\\g++.exe  @CMakeFiles/dual_rsa_crypto.dir/includes_CXX.rsp  -Wall -Wextra -Wpedantic -g -O0 -std=c++17 -o CMakeFiles\\dual_rsa_crypto.dir\\src\\crt_solver.cpp.obj -c F:\\Projects\\DualKeyEncryption\\src\\crt_solver.cpp", "file": "F:/Projects/DualKeyEncryption/src/crt_solver.cpp", "output": "CMakeFiles/dual_rsa_crypto.dir/src/crt_solver.cpp.obj"}, {"directory": "F:/Projects/DualKeyEncryption/build", "command": "D:\\msys64\\mingw64\\bin\\g++.exe  @CMakeFiles/test_dual_rsa.dir/includes_CXX.rsp  -Wall -Wextra -Wpedantic -g -O0 -std=c++17 -o CMakeFiles\\test_dual_rsa.dir\\tests\\test_dual_rsa.cpp.obj -c F:\\Projects\\DualKeyEncryption\\tests\\test_dual_rsa.cpp", "file": "F:/Projects/DualKeyEncryption/tests/test_dual_rsa.cpp", "output": "CMakeFiles/test_dual_rsa.dir/tests/test_dual_rsa.cpp.obj"}, {"directory": "F:/Projects/DualKeyEncryption/build", "command": "D:\\msys64\\mingw64\\bin\\g++.exe  @CMakeFiles/simple_test.dir/includes_CXX.rsp  -Wall -Wextra -Wpedantic -g -O0 -std=c++17 -o CMakeFiles\\simple_test.dir\\tests\\simple_test.cpp.obj -c F:\\Projects\\DualKeyEncryption\\tests\\simple_test.cpp", "file": "F:/Projects/DualKeyEncryption/tests/simple_test.cpp", "output": "CMakeFiles/simple_test.dir/tests/simple_test.cpp.obj"}, {"directory": "F:/Projects/DualKeyEncryption/build", "command": "D:\\msys64\\mingw64\\bin\\g++.exe  @CMakeFiles/debug_test.dir/includes_CXX.rsp  -Wall -Wextra -Wpedantic -g -O0 -std=c++17 -o CMakeFiles\\debug_test.dir\\tests\\debug_test.cpp.obj -c F:\\Projects\\DualKeyEncryption\\tests\\debug_test.cpp", "file": "F:/Projects/DualKeyEncryption/tests/debug_test.cpp", "output": "CMakeFiles/debug_test.dir/tests/debug_test.cpp.obj"}, {"directory": "F:/Projects/DualKeyEncryption/build", "command": "D:\\msys64\\mingw64\\bin\\g++.exe  @CMakeFiles/core_test.dir/includes_CXX.rsp  -Wall -Wextra -Wpedantic -g -O0 -std=c++17 -o CMakeFiles\\core_test.dir\\tests\\core_test.cpp.obj -c F:\\Projects\\DualKeyEncryption\\tests\\core_test.cpp", "file": "F:/Projects/DualKeyEncryption/tests/core_test.cpp", "output": "CMakeFiles/core_test.dir/tests/core_test.cpp.obj"}, {"directory": "F:/Projects/DualKeyEncryption/build", "command": "D:\\msys64\\mingw64\\bin\\g++.exe  @CMakeFiles/api_compatibility_test.dir/includes_CXX.rsp  -Wall -Wextra -Wpedantic -g -O0 -std=c++17 -o CMakeFiles\\api_compatibility_test.dir\\tests\\api_compatibility_test.cpp.obj -c F:\\Projects\\DualKeyEncryption\\tests\\api_compatibility_test.cpp", "file": "F:/Projects/DualKeyEncryption/tests/api_compatibility_test.cpp", "output": "CMakeFiles/api_compatibility_test.dir/tests/api_compatibility_test.cpp.obj"}]