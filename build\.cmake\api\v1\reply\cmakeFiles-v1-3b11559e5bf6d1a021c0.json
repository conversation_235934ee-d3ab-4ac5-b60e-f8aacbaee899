{"inputs": [{"path": "CMakeLists.txt"}, {"isGenerated": true, "path": "build/CMakeFiles/3.30.3/CMakeSystem.cmake"}, {"isCMake": true, "isExternal": true, "path": "E:/cmake/share/cmake-3.30/Modules/CMakeSystemSpecificInitialize.cmake"}, {"isCMake": true, "isExternal": true, "path": "E:/cmake/share/cmake-3.30/Modules/Platform/Windows-Initialize.cmake"}, {"isGenerated": true, "path": "build/CMakeFiles/3.30.3/CMakeCXXCompiler.cmake"}, {"isCMake": true, "isExternal": true, "path": "E:/cmake/share/cmake-3.30/Modules/CMakeSystemSpecificInformation.cmake"}, {"isCMake": true, "isExternal": true, "path": "E:/cmake/share/cmake-3.30/Modules/CMakeGenericSystem.cmake"}, {"isCMake": true, "isExternal": true, "path": "E:/cmake/share/cmake-3.30/Modules/CMakeInitializeConfigs.cmake"}, {"isCMake": true, "isExternal": true, "path": "E:/cmake/share/cmake-3.30/Modules/Platform/Windows.cmake"}, {"isCMake": true, "isExternal": true, "path": "E:/cmake/share/cmake-3.30/Modules/Platform/WindowsPaths.cmake"}, {"isCMake": true, "isExternal": true, "path": "E:/cmake/share/cmake-3.30/Modules/CMakeCXXInformation.cmake"}, {"isCMake": true, "isExternal": true, "path": "E:/cmake/share/cmake-3.30/Modules/CMakeLanguageInformation.cmake"}, {"isCMake": true, "isExternal": true, "path": "E:/cmake/share/cmake-3.30/Modules/Compiler/GNU-CXX.cmake"}, {"isCMake": true, "isExternal": true, "path": "E:/cmake/share/cmake-3.30/Modules/Compiler/GNU.cmake"}, {"isCMake": true, "isExternal": true, "path": "E:/cmake/share/cmake-3.30/Modules/Compiler/CMakeCommonCompilerMacros.cmake"}, {"isCMake": true, "isExternal": true, "path": "E:/cmake/share/cmake-3.30/Modules/Platform/Windows-GNU-CXX.cmake"}, {"isCMake": true, "isExternal": true, "path": "E:/cmake/share/cmake-3.30/Modules/Platform/Windows-GNU.cmake"}, {"isGenerated": true, "path": "build/CMakeFiles/3.30.3/CMakeRCCompiler.cmake"}, {"isCMake": true, "isExternal": true, "path": "E:/cmake/share/cmake-3.30/Modules/CMakeRCInformation.cmake"}, {"isCMake": true, "isExternal": true, "path": "E:/cmake/share/cmake-3.30/Modules/Platform/Windows-windres.cmake"}, {"isCMake": true, "isExternal": true, "path": "E:/cmake/share/cmake-3.30/Modules/Platform/Windows-GNU-CXX-ABI.cmake"}, {"isCMake": true, "isExternal": true, "path": "E:/cmake/share/cmake-3.30/Modules/CMakeCommonLanguageInclude.cmake"}, {"isCMake": true, "isExternal": true, "path": "E:/cmake/share/cmake-3.30/Modules/FindOpenSSL.cmake"}, {"isCMake": true, "isExternal": true, "path": "E:/cmake/share/cmake-3.30/Modules/FindPackageHandleStandardArgs.cmake"}, {"isCMake": true, "isExternal": true, "path": "E:/cmake/share/cmake-3.30/Modules/FindPackageMessage.cmake"}], "kind": "cmakeFiles", "paths": {"build": "F:/Projects/DualKeyEncryption/build", "source": "F:/Projects/DualKeyEncryption"}, "version": {"major": 1, "minor": 1}}