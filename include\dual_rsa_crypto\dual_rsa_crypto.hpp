#pragma once

#include "rsa_keys.hpp"
#include <vector>
#include <cstdint>
#include <stdexcept>

namespace dual_rsa_crypto {

/**
 * @brief 填充模式枚举
 */
enum class PaddingMode {
    PKCS1,  ///< PKCS#1 v1.5 填充
    OAEP    ///< OAEP 填充
};

/**
 * @brief 双密钥加密异常类
 */
class DualRSAException : public std::runtime_error {
public:
    explicit DualRSAException(const std::string& message) 
        : std::runtime_error(message) {}
};

/**
 * @brief 双密钥RSA加密器
 * 
 * 使用PImpl设计模式隐藏实现细节
 */
class DualRSAEncryptor {
public:
    /**
     * @brief 构造函数
     */
    DualRSAEncryptor();
    
    /**
     * @brief 析构函数
     */
    ~DualRSAEncryptor();
    
    // 禁用拷贝构造和赋值
    DualRSAEncryptor(const DualRSAEncryptor&) = delete;
    DualRSAEncryptor& operator=(const DualRSAEncryptor&) = delete;
    
    // 启用移动构造和赋值
    DualRSAEncryptor(DualRSAEncryptor&& other) noexcept;
    DualRSAEncryptor& operator=(DualRSAEncryptor&& other) noexcept;
    
    /**
     * @brief 双密钥加密函数
     * 
     * 将两个不同的明文使用两个不同的RSA公钥加密生成单一密文
     * 
     * @param plaintext1 第一个明文
     * @param plaintext2 第二个明文
     * @param public_key1 第一个RSA公钥
     * @param public_key2 第二个RSA公钥
     * @param padding 填充模式，默认为PKCS1
     * @return 统一密文
     * 
     * @throws DualRSAException 当加密失败时
     * @throws RSAKeyException 当密钥无效时
     */
    std::vector<uint8_t> encrypt_dual(
        const std::vector<uint8_t>& plaintext1,
        const std::vector<uint8_t>& plaintext2,
        const RSAPublicKey& public_key1,
        const RSAPublicKey& public_key2,
        PaddingMode padding = PaddingMode::PKCS1
    );
    
    /**
     * @brief 单密钥解密函数
     * 
     * 使用私钥对统一密文进行解密，恢复对应的原始明文
     * 
     * @param ciphertext 统一密文
     * @param private_key RSA私钥
     * @param padding 填充模式，默认为PKCS1
     * @return 原始明文
     * 
     * @throws DualRSAException 当解密失败时
     * @throws RSAKeyException 当密钥无效时
     */
    std::vector<uint8_t> decrypt_single(
        const std::vector<uint8_t>& ciphertext,
        const RSAPrivateKey& private_key,
        PaddingMode padding = PaddingMode::PKCS1
    );
    
    /**
     * @brief 验证两个公钥是否可以用于双密钥加密
     * 
     * 检查两个RSA公钥的模数是否互质
     * 
     * @param public_key1 第一个RSA公钥
     * @param public_key2 第二个RSA公钥
     * @return 如果可以使用返回true，否则返回false
     */
    static bool validateKeyPair(const RSAPublicKey& public_key1, 
                               const RSAPublicKey& public_key2);
    
    /**
     * @brief 获取给定密钥对的最大明文长度
     * 
     * @param public_key RSA公钥
     * @param padding 填充模式
     * @return 最大明文长度（字节）
     */
    static size_t getMaxPlaintextLength(const RSAPublicKey& public_key, 
                                       PaddingMode padding = PaddingMode::PKCS1);
    
    /**
     * @brief 获取给定密钥对的密文长度
     * 
     * @param public_key RSA公钥
     * @return 密文长度（字节）
     */
    static size_t getCiphertextLength(const RSAPublicKey& public_key);

private:
    class Impl;
    std::unique_ptr<Impl> pImpl;
};

// 便利函数

/**
 * @brief 双密钥加密便利函数
 * 
 * @param plaintext1 第一个明文
 * @param plaintext2 第二个明文
 * @param public_key1 第一个RSA公钥
 * @param public_key2 第二个RSA公钥
 * @param padding 填充模式，默认为PKCS1
 * @return 统一密文
 */
std::vector<uint8_t> encrypt_dual(
    const std::vector<uint8_t>& plaintext1,
    const std::vector<uint8_t>& plaintext2,
    const RSAPublicKey& public_key1,
    const RSAPublicKey& public_key2,
    PaddingMode padding = PaddingMode::PKCS1
);

/**
 * @brief 单密钥解密便利函数
 * 
 * @param ciphertext 统一密文
 * @param private_key RSA私钥
 * @param padding 填充模式，默认为PKCS1
 * @return 原始明文
 */
std::vector<uint8_t> decrypt_single(
    const std::vector<uint8_t>& ciphertext,
    const RSAPrivateKey& private_key,
    PaddingMode padding = PaddingMode::PKCS1
);

// CRT求解器函数声明（在crt_solver.cpp中实现）

/**
 * @brief 中国剩余定理求解函数
 * 
 * @param a1 第一个余数
 * @param n1 第一个模数
 * @param a2 第二个余数
 * @param n2 第二个模数
 * @return CRT解
 */
std::vector<uint8_t> solveCRT(const std::vector<uint8_t>& a1, 
                             const std::vector<uint8_t>& n1,
                             const std::vector<uint8_t>& a2, 
                             const std::vector<uint8_t>& n2);

/**
 * @brief 验证CRT解的正确性
 * 
 * @param x CRT解
 * @param a1 第一个余数
 * @param n1 第一个模数
 * @param a2 第二个余数
 * @param n2 第二个模数
 * @return 如果解正确返回true
 */
bool verifyCRT(const std::vector<uint8_t>& x,
               const std::vector<uint8_t>& a1, 
               const std::vector<uint8_t>& n1,
               const std::vector<uint8_t>& a2, 
               const std::vector<uint8_t>& n2);

} // namespace dual_rsa_crypto
