# CMAKE generated file: DO NOT EDIT!
# Generated by "MinGW Makefiles" Generator, CMake Version 3.30

# Default target executed when no arguments are given to make.
default_target: all
.PHONY : default_target

#=============================================================================
# Special targets provided by cmake.

# Disable implicit rules so canonical targets will work.
.SUFFIXES:

# Disable VCS-based implicit rules.
% : %,v

# Disable VCS-based implicit rules.
% : RCS/%

# Disable VCS-based implicit rules.
% : RCS/%,v

# Disable VCS-based implicit rules.
% : SCCS/s.%

# Disable VCS-based implicit rules.
% : s.%

.SUFFIXES: .hpux_make_needs_suffix_list

# Command-line flag to silence nested $(MAKE).
$(VERBOSE)MAKESILENT = -s

#Suppress display of executed commands.
$(VERBOSE).SILENT:

# A target that is always out of date.
cmake_force:
.PHONY : cmake_force

#=============================================================================
# Set environment variables for the build.

SHELL = cmd.exe

# The CMake executable.
CMAKE_COMMAND = E:\cmake\bin\cmake.exe

# The command to remove a file.
RM = E:\cmake\bin\cmake.exe -E rm -f

# Escaping for special characters.
EQUALS = =

# The top-level source directory on which CMake was run.
CMAKE_SOURCE_DIR = F:\Projects\DualKeyEncryption

# The top-level build directory on which CMake was run.
CMAKE_BINARY_DIR = F:\Projects\DualKeyEncryption\build

#=============================================================================
# Directory level rules for the build root directory

# The main recursive "all" target.
all: CMakeFiles/dual_rsa_crypto.dir/all
all: CMakeFiles/test_dual_rsa.dir/all
all: CMakeFiles/simple_test.dir/all
all: CMakeFiles/debug_test.dir/all
.PHONY : all

# The main recursive "preinstall" target.
preinstall:
.PHONY : preinstall

# The main recursive "clean" target.
clean: CMakeFiles/dual_rsa_crypto.dir/clean
clean: CMakeFiles/test_dual_rsa.dir/clean
clean: CMakeFiles/simple_test.dir/clean
clean: CMakeFiles/debug_test.dir/clean
.PHONY : clean

#=============================================================================
# Target rules for target CMakeFiles/dual_rsa_crypto.dir

# All Build rule for target.
CMakeFiles/dual_rsa_crypto.dir/all:
	$(MAKE) $(MAKESILENT) -f CMakeFiles\dual_rsa_crypto.dir\build.make CMakeFiles/dual_rsa_crypto.dir/depend
	$(MAKE) $(MAKESILENT) -f CMakeFiles\dual_rsa_crypto.dir\build.make CMakeFiles/dual_rsa_crypto.dir/build
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --progress-dir=F:\Projects\DualKeyEncryption\build\CMakeFiles --progress-num=3,4,5,6 "Built target dual_rsa_crypto"
.PHONY : CMakeFiles/dual_rsa_crypto.dir/all

# Build rule for subdir invocation for target.
CMakeFiles/dual_rsa_crypto.dir/rule: cmake_check_build_system
	$(CMAKE_COMMAND) -E cmake_progress_start F:\Projects\DualKeyEncryption\build\CMakeFiles 4
	$(MAKE) $(MAKESILENT) -f CMakeFiles\Makefile2 CMakeFiles/dual_rsa_crypto.dir/all
	$(CMAKE_COMMAND) -E cmake_progress_start F:\Projects\DualKeyEncryption\build\CMakeFiles 0
.PHONY : CMakeFiles/dual_rsa_crypto.dir/rule

# Convenience name for target.
dual_rsa_crypto: CMakeFiles/dual_rsa_crypto.dir/rule
.PHONY : dual_rsa_crypto

# clean rule for target.
CMakeFiles/dual_rsa_crypto.dir/clean:
	$(MAKE) $(MAKESILENT) -f CMakeFiles\dual_rsa_crypto.dir\build.make CMakeFiles/dual_rsa_crypto.dir/clean
.PHONY : CMakeFiles/dual_rsa_crypto.dir/clean

#=============================================================================
# Target rules for target CMakeFiles/test_dual_rsa.dir

# All Build rule for target.
CMakeFiles/test_dual_rsa.dir/all: CMakeFiles/dual_rsa_crypto.dir/all
	$(MAKE) $(MAKESILENT) -f CMakeFiles\test_dual_rsa.dir\build.make CMakeFiles/test_dual_rsa.dir/depend
	$(MAKE) $(MAKESILENT) -f CMakeFiles\test_dual_rsa.dir\build.make CMakeFiles/test_dual_rsa.dir/build
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --progress-dir=F:\Projects\DualKeyEncryption\build\CMakeFiles --progress-num=9,10 "Built target test_dual_rsa"
.PHONY : CMakeFiles/test_dual_rsa.dir/all

# Build rule for subdir invocation for target.
CMakeFiles/test_dual_rsa.dir/rule: cmake_check_build_system
	$(CMAKE_COMMAND) -E cmake_progress_start F:\Projects\DualKeyEncryption\build\CMakeFiles 6
	$(MAKE) $(MAKESILENT) -f CMakeFiles\Makefile2 CMakeFiles/test_dual_rsa.dir/all
	$(CMAKE_COMMAND) -E cmake_progress_start F:\Projects\DualKeyEncryption\build\CMakeFiles 0
.PHONY : CMakeFiles/test_dual_rsa.dir/rule

# Convenience name for target.
test_dual_rsa: CMakeFiles/test_dual_rsa.dir/rule
.PHONY : test_dual_rsa

# clean rule for target.
CMakeFiles/test_dual_rsa.dir/clean:
	$(MAKE) $(MAKESILENT) -f CMakeFiles\test_dual_rsa.dir\build.make CMakeFiles/test_dual_rsa.dir/clean
.PHONY : CMakeFiles/test_dual_rsa.dir/clean

#=============================================================================
# Target rules for target CMakeFiles/simple_test.dir

# All Build rule for target.
CMakeFiles/simple_test.dir/all: CMakeFiles/dual_rsa_crypto.dir/all
	$(MAKE) $(MAKESILENT) -f CMakeFiles\simple_test.dir\build.make CMakeFiles/simple_test.dir/depend
	$(MAKE) $(MAKESILENT) -f CMakeFiles\simple_test.dir\build.make CMakeFiles/simple_test.dir/build
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --progress-dir=F:\Projects\DualKeyEncryption\build\CMakeFiles --progress-num=7,8 "Built target simple_test"
.PHONY : CMakeFiles/simple_test.dir/all

# Build rule for subdir invocation for target.
CMakeFiles/simple_test.dir/rule: cmake_check_build_system
	$(CMAKE_COMMAND) -E cmake_progress_start F:\Projects\DualKeyEncryption\build\CMakeFiles 6
	$(MAKE) $(MAKESILENT) -f CMakeFiles\Makefile2 CMakeFiles/simple_test.dir/all
	$(CMAKE_COMMAND) -E cmake_progress_start F:\Projects\DualKeyEncryption\build\CMakeFiles 0
.PHONY : CMakeFiles/simple_test.dir/rule

# Convenience name for target.
simple_test: CMakeFiles/simple_test.dir/rule
.PHONY : simple_test

# clean rule for target.
CMakeFiles/simple_test.dir/clean:
	$(MAKE) $(MAKESILENT) -f CMakeFiles\simple_test.dir\build.make CMakeFiles/simple_test.dir/clean
.PHONY : CMakeFiles/simple_test.dir/clean

#=============================================================================
# Target rules for target CMakeFiles/debug_test.dir

# All Build rule for target.
CMakeFiles/debug_test.dir/all:
	$(MAKE) $(MAKESILENT) -f CMakeFiles\debug_test.dir\build.make CMakeFiles/debug_test.dir/depend
	$(MAKE) $(MAKESILENT) -f CMakeFiles\debug_test.dir\build.make CMakeFiles/debug_test.dir/build
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --progress-dir=F:\Projects\DualKeyEncryption\build\CMakeFiles --progress-num=1,2 "Built target debug_test"
.PHONY : CMakeFiles/debug_test.dir/all

# Build rule for subdir invocation for target.
CMakeFiles/debug_test.dir/rule: cmake_check_build_system
	$(CMAKE_COMMAND) -E cmake_progress_start F:\Projects\DualKeyEncryption\build\CMakeFiles 2
	$(MAKE) $(MAKESILENT) -f CMakeFiles\Makefile2 CMakeFiles/debug_test.dir/all
	$(CMAKE_COMMAND) -E cmake_progress_start F:\Projects\DualKeyEncryption\build\CMakeFiles 0
.PHONY : CMakeFiles/debug_test.dir/rule

# Convenience name for target.
debug_test: CMakeFiles/debug_test.dir/rule
.PHONY : debug_test

# clean rule for target.
CMakeFiles/debug_test.dir/clean:
	$(MAKE) $(MAKESILENT) -f CMakeFiles\debug_test.dir\build.make CMakeFiles/debug_test.dir/clean
.PHONY : CMakeFiles/debug_test.dir/clean

#=============================================================================
# Special targets to cleanup operation of make.

# Special rule to run CMake to check the build system integrity.
# No rule that depends on this can have commands that come from listfiles
# because they might be regenerated.
cmake_check_build_system:
	$(CMAKE_COMMAND) -S$(CMAKE_SOURCE_DIR) -B$(CMAKE_BINARY_DIR) --check-build-system CMakeFiles\Makefile.cmake 0
.PHONY : cmake_check_build_system

