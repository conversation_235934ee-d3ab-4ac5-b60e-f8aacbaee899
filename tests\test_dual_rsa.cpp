#include "dual_rsa_crypto/dual_rsa_crypto.hpp"
#include <iostream>
#include <cassert>
#include <string>
#include <vector>
#include <chrono>
#include <random>

using namespace dual_rsa_crypto;

// Test helper functions
namespace {
    // Generate random byte array
    std::vector<uint8_t> generateRandomBytes(size_t length) {
        std::random_device rd;
        std::mt19937 gen(rd());
        std::uniform_int_distribution<uint8_t> dis(0, 255);

        std::vector<uint8_t> result(length);
        for (size_t i = 0; i < length; ++i) {
            result[i] = dis(gen);
        }
        return result;
    }

    // String to byte array
    std::vector<uint8_t> stringToBytes(const std::string& str) {
        return std::vector<uint8_t>(str.begin(), str.end());
    }

    // Byte array to string
    std::string bytesToString(const std::vector<uint8_t>& bytes) {
        return std::string(bytes.begin(), bytes.end());
    }

    // Print byte array (hexadecimal)
    void printBytes(const std::vector<uint8_t>& bytes, const std::string& label) {
        std::cout << label << " (" << bytes.size() << " bytes): ";
        for (size_t i = 0; i < std::min(bytes.size(), size_t(16)); ++i) {
            printf("%02x ", bytes[i]);
        }
        if (bytes.size() > 16) {
            std::cout << "...";
        }
        std::cout << std::endl;
    }

    // Test timer
    class Timer {
    public:
        Timer(const std::string& name) : name_(name), start_(std::chrono::high_resolution_clock::now()) {}

        ~Timer() {
            auto end = std::chrono::high_resolution_clock::now();
            auto duration = std::chrono::duration_cast<std::chrono::milliseconds>(end - start_);
            std::cout << name_ << " took " << duration.count() << " ms" << std::endl;
        }

    private:
        std::string name_;
        std::chrono::high_resolution_clock::time_point start_;
    };
}

// Test case class
class DualRSATest {
public:
    static void runAllTests() {
        std::cout << "=== Dual-Key RSA Encryption Module Tests ===" << std::endl;

        testKeyGeneration();
        testKeyValidation();
        testBasicEncryptionDecryption();
        testDifferentKeySizes();
        testDifferentPaddingModes();
        testLargeMessages();
        testErrorHandling();
        testCRTSolver();
        testPerformance();

        std::cout << "\n=== All tests passed! ===" << std::endl;
    }
    
private:
    static void testKeyGeneration() {
        std::cout << "\n--- Testing Key Generation ---" << std::endl;
        Timer timer("Key generation test");

        // Test generating different key sizes
        for (int key_size : {1024}) {
            std::cout << "Generating " << key_size << " bit key pairs..." << std::endl;

            std::cout << "Generating first key pair..." << std::endl;
            auto [private_key1, public_key1] = generateRSAKeyPair(key_size);
            std::cout << "Generating second key pair..." << std::endl;
            auto [private_key2, public_key2] = generateRSAKeyPair(key_size);

            // Verify key properties
            std::cout << "Verifying key properties..." << std::endl;
            assert(public_key1.getKeySize() == key_size);
            assert(public_key2.getKeySize() == key_size);
            assert(private_key1.getKeySize() == key_size);
            assert(private_key2.getKeySize() == key_size);

            // Verify moduli are different
            std::cout << "Getting moduli..." << std::endl;
            auto n1 = public_key1.getModulus();
            auto n2 = public_key2.getModulus();
            std::cout << "Verifying moduli are different..." << std::endl;
            assert(n1 != n2);

            // Verify moduli are coprime
            bool coprime = areModuliCoprime(n1, n2);
            if (!coprime) {
                std::cout << "Warning: Generated key moduli are not coprime, regenerating..." << std::endl;
                // Regenerate second key pair
                auto [new_private_key2, new_public_key2] = generateRSAKeyPair(key_size);
                private_key2 = std::move(new_private_key2);
                public_key2 = std::move(new_public_key2);
                n2 = public_key2.getModulus();
                coprime = areModuliCoprime(n1, n2);
            }
            assert(coprime);

            std::cout << "✓ " << key_size << " bit key generation successful" << std::endl;
        }
    }
    
    static void testKeyValidation() {
        std::cout << "\n--- 测试密钥验证 ---" << std::endl;
        Timer timer("Key validation test");
        
        auto [private_key1, public_key1] = generateRSAKeyPair(2048);
        auto [private_key2, public_key2] = generateRSAKeyPair(2048);
        
        // 测试有效密钥对
        assert(DualRSAEncryptor::validateKeyPair(public_key1, public_key2));
        std::cout << "✓ 有效密钥对验证通过" << std::endl;
        
        // 测试相同密钥（应该失败，因为模数相同）
        assert(!DualRSAEncryptor::validateKeyPair(public_key1, public_key1));
        std::cout << "✓ 相同密钥验证正确失败" << std::endl;
    }
    
    static void testBasicEncryptionDecryption() {
        std::cout << "\n--- 测试基本加密解密 ---" << std::endl;
        Timer timer("Basic encryption/decryption test");
        
        // 生成密钥对
        auto [private_key1, public_key1] = generateRSAKeyPair(2048);
        auto [private_key2, public_key2] = generateRSAKeyPair(2048);
        
        // 准备测试数据
        std::string message1 = "这是第一个秘密消息";
        std::string message2 = "这是第二个秘密消息";
        
        auto plaintext1 = stringToBytes(message1);
        auto plaintext2 = stringToBytes(message2);
        
        std::cout << "原始消息1: " << message1 << std::endl;
        std::cout << "原始消息2: " << message2 << std::endl;
        
        // 双密钥加密
        DualRSAEncryptor encryptor;
        auto ciphertext = encryptor.encrypt_dual(plaintext1, plaintext2, public_key1, public_key2);
        
        printBytes(ciphertext, "统一密文");
        
        // 使用第一个私钥解密
        auto decrypted1 = encryptor.decrypt_single(ciphertext, private_key1);
        std::string recovered1 = bytesToString(decrypted1);
        
        // 使用第二个私钥解密
        auto decrypted2 = encryptor.decrypt_single(ciphertext, private_key2);
        std::string recovered2 = bytesToString(decrypted2);
        
        std::cout << "解密消息1: " << recovered1 << std::endl;
        std::cout << "解密消息2: " << recovered2 << std::endl;
        
        // 验证解密结果
        assert(recovered1 == message1);
        assert(recovered2 == message2);
        
        std::cout << "✓ 基本加密解密测试通过" << std::endl;
    }
    
    static void testDifferentKeySizes() {
        std::cout << "\n--- 测试不同密钥长度 ---" << std::endl;
        Timer timer("Different key sizes test");
        
        for (int key_size : {1024, 2048}) {
            std::cout << "测试 " << key_size << " 位密钥..." << std::endl;
            
            auto [private_key1, public_key1] = generateRSAKeyPair(key_size);
            auto [private_key2, public_key2] = generateRSAKeyPair(key_size);
            
            // 计算最大明文长度
            size_t max_len = DualRSAEncryptor::getMaxPlaintextLength(public_key1, PaddingMode::PKCS1);
            std::cout << "最大明文长度: " << max_len << " 字节" << std::endl;
            
            // 生成接近最大长度的随机数据
            auto plaintext1 = generateRandomBytes(max_len - 10);
            auto plaintext2 = generateRandomBytes(max_len - 20);
            
            // 加密解密测试
            auto ciphertext = encrypt_dual(plaintext1, plaintext2, public_key1, public_key2);
            auto decrypted1 = decrypt_single(ciphertext, private_key1);
            auto decrypted2 = decrypt_single(ciphertext, private_key2);
            
            assert(decrypted1 == plaintext1);
            assert(decrypted2 == plaintext2);
            
            std::cout << "✓ " << key_size << " 位密钥测试通过" << std::endl;
        }
    }
    
    static void testDifferentPaddingModes() {
        std::cout << "\n--- 测试不同填充模式 ---" << std::endl;
        Timer timer("Different padding modes test");
        
        auto [private_key1, public_key1] = generateRSAKeyPair(2048);
        auto [private_key2, public_key2] = generateRSAKeyPair(2048);
        
        std::string message1 = "PKCS1测试消息";
        std::string message2 = "OAEP测试消息";
        
        auto plaintext1 = stringToBytes(message1);
        auto plaintext2 = stringToBytes(message2);
        
        // 测试PKCS1填充
        {
            std::cout << "测试PKCS#1填充..." << std::endl;
            auto ciphertext = encrypt_dual(plaintext1, plaintext2, public_key1, public_key2, PaddingMode::PKCS1);
            auto decrypted1 = decrypt_single(ciphertext, private_key1, PaddingMode::PKCS1);
            auto decrypted2 = decrypt_single(ciphertext, private_key2, PaddingMode::PKCS1);
            
            assert(bytesToString(decrypted1) == message1);
            assert(bytesToString(decrypted2) == message2);
            std::cout << "✓ PKCS#1填充测试通过" << std::endl;
        }
        
        // 测试OAEP填充
        {
            std::cout << "测试OAEP填充..." << std::endl;
            auto ciphertext = encrypt_dual(plaintext1, plaintext2, public_key1, public_key2, PaddingMode::OAEP);
            auto decrypted1 = decrypt_single(ciphertext, private_key1, PaddingMode::OAEP);
            auto decrypted2 = decrypt_single(ciphertext, private_key2, PaddingMode::OAEP);
            
            assert(bytesToString(decrypted1) == message1);
            assert(bytesToString(decrypted2) == message2);
            std::cout << "✓ OAEP填充测试通过" << std::endl;
        }
    }
    
    static void testLargeMessages() {
        std::cout << "\n--- 测试大消息 ---" << std::endl;
        Timer timer("Large messages test");
        
        auto [private_key1, public_key1] = generateRSAKeyPair(2048);
        auto [private_key2, public_key2] = generateRSAKeyPair(2048);
        
        // 生成大的随机消息
        size_t max_len = DualRSAEncryptor::getMaxPlaintextLength(public_key1, PaddingMode::PKCS1);
        auto large_message1 = generateRandomBytes(max_len);
        auto large_message2 = generateRandomBytes(max_len - 1);
        
        std::cout << "消息1长度: " << large_message1.size() << " 字节" << std::endl;
        std::cout << "消息2长度: " << large_message2.size() << " 字节" << std::endl;
        
        auto ciphertext = encrypt_dual(large_message1, large_message2, public_key1, public_key2);
        auto decrypted1 = decrypt_single(ciphertext, private_key1);
        auto decrypted2 = decrypt_single(ciphertext, private_key2);
        
        assert(decrypted1 == large_message1);
        assert(decrypted2 == large_message2);
        
        std::cout << "✓ 大消息测试通过" << std::endl;
    }
    
    static void testErrorHandling() {
        std::cout << "\n--- 测试错误处理 ---" << std::endl;
        Timer timer("Error handling test");
        
        auto [private_key1, public_key1] = generateRSAKeyPair(1024);
        auto [private_key2, public_key2] = generateRSAKeyPair(1024);
        
        // 测试过长的明文
        size_t max_len = DualRSAEncryptor::getMaxPlaintextLength(public_key1, PaddingMode::PKCS1);
        auto too_long_message = generateRandomBytes(max_len + 1);
        auto normal_message = generateRandomBytes(10);
        
        try {
            encrypt_dual(too_long_message, normal_message, public_key1, public_key2);
            assert(false); // 应该抛出异常
        } catch (const DualRSAException& e) {
            std::cout << "✓ 正确捕获过长明文异常: " << e.what() << std::endl;
        }
        
        // 测试无效密文解密（使用明确无效的密文）
        std::vector<uint8_t> invalid_ciphertext(private_key1.getKeySize() / 8, 0xFF);
        try {
            decrypt_single(invalid_ciphertext, private_key1);
            std::cout << "警告: 解密无效密文没有抛出异常，可能是OpenSSL的容错处理" << std::endl;
        } catch (const DualRSAException& e) {
            std::cout << "✓ 正确捕获无效密文异常: " << e.what() << std::endl;
        } catch (const std::exception& e) {
            std::cout << "✓ 捕获到异常: " << e.what() << std::endl;
        }
    }
    
    static void testCRTSolver() {
        std::cout << "\n--- 测试中国剩余定理求解器 ---" << std::endl;
        Timer timer("CRT solver test");
        
        // 使用小的测试数据
        std::vector<uint8_t> a1 = {2};  // 2
        std::vector<uint8_t> n1 = {3};  // 3
        std::vector<uint8_t> a2 = {3};  // 3
        std::vector<uint8_t> n2 = {5};  // 5
        
        // 求解 x ≡ 2 (mod 3), x ≡ 3 (mod 5)
        // 解应该是 x = 8 (mod 15)
        auto solution = solveCRT(a1, n1, a2, n2);
        
        // 验证解
        assert(verifyCRT(solution, a1, n1, a2, n2));
        
        std::cout << "✓ CRT求解器测试通过" << std::endl;
    }
    
    static void testPerformance() {
        std::cout << "\n--- 性能测试 ---" << std::endl;
        
        auto [private_key1, public_key1] = generateRSAKeyPair(2048);
        auto [private_key2, public_key2] = generateRSAKeyPair(2048);
        
        auto message1 = generateRandomBytes(100);
        auto message2 = generateRandomBytes(100);
        
        const int iterations = 10;
        
        {
            Timer timer("10次双密钥加密");
            for (int i = 0; i < iterations; ++i) {
                auto ciphertext = encrypt_dual(message1, message2, public_key1, public_key2);
            }
        }
        
        auto ciphertext = encrypt_dual(message1, message2, public_key1, public_key2);
        
        {
            Timer timer("10次单密钥解密");
            for (int i = 0; i < iterations; ++i) {
                auto decrypted = decrypt_single(ciphertext, private_key1);
            }
        }
        
        std::cout << "✓ 性能测试完成" << std::endl;
    }
};

int main() {
    try {
        DualRSATest::runAllTests();
        return 0;
    } catch (const std::exception& e) {
        std::cerr << "测试失败: " << e.what() << std::endl;
        return 1;
    }
}
