E:\cmake\bin\cmake.exe -E rm -f CMakeFiles\api_compatibility_test.dir/objects.a
D:\msys64\mingw64\bin\ar.exe qc CMakeFiles\api_compatibility_test.dir/objects.a @CMakeFiles\api_compatibility_test.dir\objects1.rsp
D:\msys64\mingw64\bin\g++.exe  -Wall -Wextra -Wpedantic -g -O0 -Wl,--whole-archive CMakeFiles\api_compatibility_test.dir/objects.a -Wl,--no-whole-archive -o api_compatibility_test.exe -Wl,--out-implib,libapi_compatibility_test.dll.a -Wl,--major-image-version,0,--minor-image-version,0 @CMakeFiles\api_compatibility_test.dir\linkLibs.rsp
