{"archive": {}, "artifacts": [{"path": "libdual_rsa_crypto.a"}], "backtrace": 1, "backtraceGraph": {"commands": ["add_library", "install", "include_directories"], "files": ["CMakeLists.txt"], "nodes": [{"file": 0}, {"command": 0, "file": 0, "line": 38, "parent": 0}, {"command": 1, "file": 0, "line": 77, "parent": 0}, {"command": 2, "file": 0, "line": 28, "parent": 0}]}, "compileGroups": [{"compileCommandFragments": [{"fragment": " -Wall -Wextra -Wpedantic -g -O0 -std=c++17"}], "includes": [{"backtrace": 3, "path": "F:/Projects/DualKeyEncryption/include"}], "language": "CXX", "languageStandard": {"backtraces": [1], "standard": "17"}, "sourceIndexes": [0, 1, 2]}], "id": "dual_rsa_crypto::@6890427a1f51a3e7e1df", "install": {"destinations": [{"backtrace": 2, "path": "lib"}], "prefix": {"path": "C:/Program Files (x86)/DualRSACrypto"}}, "name": "dual_rsa_crypto", "nameOnDisk": "libdual_rsa_crypto.a", "paths": {"build": ".", "source": "."}, "sourceGroups": [{"name": "Source Files", "sourceIndexes": [0, 1, 2]}], "sources": [{"backtrace": 1, "compileGroupIndex": 0, "path": "src/dual_rsa_crypto.cpp", "sourceGroupIndex": 0}, {"backtrace": 1, "compileGroupIndex": 0, "path": "src/rsa_keys.cpp", "sourceGroupIndex": 0}, {"backtrace": 1, "compileGroupIndex": 0, "path": "src/crt_solver.cpp", "sourceGroupIndex": 0}], "type": "STATIC_LIBRARY"}