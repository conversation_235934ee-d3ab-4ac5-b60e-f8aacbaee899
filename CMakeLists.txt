cmake_minimum_required(VERSION 3.15)
project(DualRSACrypto VERSION 1.0.0 LANGUAGES CXX)

# 设置C++标准
set(CMAKE_CXX_STANDARD 17)
set(CMAKE_CXX_STANDARD_REQUIRED ON)
set(CMAKE_CXX_EXTENSIONS OFF)

#生成compile_commands.json
set(CMAKE_EXPORT_COMPILE_COMMANDS ON)

# 设置编译器路径（MSYS2 MinGW64）
set(CMAKE_C_COMPILER "D:/msys64/mingw64/bin/gcc.exe")
set(CMAKE_CXX_COMPILER "D:/msys64/mingw64/bin/g++.exe")

# 设置编译选项
if(CMAKE_CXX_COMPILER_ID STREQUAL "GNU")
    set(CMAKE_CXX_FLAGS "${CMAKE_CXX_FLAGS} -Wall -Wextra -Wpedantic")
    set(CMAKE_CXX_FLAGS_DEBUG "-g -O0")
    set(CMAKE_CXX_FLAGS_RELEASE "-O3 -DNDEBUG")
endif()

# 查找OpenSSL
set(OPENSSL_ROOT_DIR "D:/msys64/mingw64")
find_package(OpenSSL REQUIRED)

# 包含目录
include_directories(include)

# 源文件
set(SOURCES
    src/dual_rsa_crypto.cpp
    src/rsa_keys.cpp
    src/crt_solver.cpp
)

# 创建静态库
add_library(dual_rsa_crypto STATIC ${SOURCES})

# 链接OpenSSL
target_link_libraries(dual_rsa_crypto 
    OpenSSL::SSL 
    OpenSSL::Crypto
)

# 设置包含目录
target_include_directories(dual_rsa_crypto PUBLIC
    $<BUILD_INTERFACE:${CMAKE_CURRENT_SOURCE_DIR}/include>
    $<INSTALL_INTERFACE:include>
)

# 启用测试
enable_testing()

# 测试可执行文件
add_executable(test_dual_rsa tests/test_dual_rsa.cpp)
target_link_libraries(test_dual_rsa dual_rsa_crypto)

# 简单测试可执行文件
add_executable(simple_test tests/simple_test.cpp)
target_link_libraries(simple_test dual_rsa_crypto)

# 调试测试可执行文件
add_executable(debug_test tests/debug_test.cpp)
target_link_libraries(debug_test OpenSSL::SSL OpenSSL::Crypto)

# 核心测试可执行文件
add_executable(core_test tests/core_test.cpp)
target_link_libraries(core_test dual_rsa_crypto)

# 添加测试
add_test(NAME DualRSATests COMMAND test_dual_rsa)
add_test(NAME SimpleTest COMMAND simple_test)
add_test(NAME CoreTest COMMAND core_test)

# 安装规则
install(TARGETS dual_rsa_crypto
    EXPORT DualRSACryptoTargets
    LIBRARY DESTINATION lib
    ARCHIVE DESTINATION lib
    RUNTIME DESTINATION bin
)

install(DIRECTORY include/ DESTINATION include)

# 导出配置
install(EXPORT DualRSACryptoTargets
    FILE DualRSACryptoTargets.cmake
    NAMESPACE DualRSACrypto::
    DESTINATION lib/cmake/DualRSACrypto
)
