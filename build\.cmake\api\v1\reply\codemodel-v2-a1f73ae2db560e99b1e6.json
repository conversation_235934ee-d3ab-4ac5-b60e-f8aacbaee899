{"configurations": [{"directories": [{"build": ".", "hasInstallRule": true, "jsonFile": "directory-.-Debug-ac2590db8f870f4adf71.json", "minimumCMakeVersion": {"string": "3.15"}, "projectIndex": 0, "source": ".", "targetIndexes": [0, 1, 2, 3, 4, 5]}], "name": "Debug", "projects": [{"directoryIndexes": [0], "name": "DualRSACrypto", "targetIndexes": [0, 1, 2, 3, 4, 5]}], "targets": [{"directoryIndex": 0, "id": "api_compatibility_test::@6890427a1f51a3e7e1df", "jsonFile": "target-api_compatibility_test-Debug-5cf10a468c1aa96e7cfe.json", "name": "api_compatibility_test", "projectIndex": 0}, {"directoryIndex": 0, "id": "core_test::@6890427a1f51a3e7e1df", "jsonFile": "target-core_test-Debug-91ff6322379e05c7115c.json", "name": "core_test", "projectIndex": 0}, {"directoryIndex": 0, "id": "debug_test::@6890427a1f51a3e7e1df", "jsonFile": "target-debug_test-Debug-23d7d3127808eb0be0fd.json", "name": "debug_test", "projectIndex": 0}, {"directoryIndex": 0, "id": "dual_rsa_crypto::@6890427a1f51a3e7e1df", "jsonFile": "target-dual_rsa_crypto-Debug-7dcf2eb91e0118358129.json", "name": "dual_rsa_crypto", "projectIndex": 0}, {"directoryIndex": 0, "id": "simple_test::@6890427a1f51a3e7e1df", "jsonFile": "target-simple_test-Debug-a802b7fdf8014d3217db.json", "name": "simple_test", "projectIndex": 0}, {"directoryIndex": 0, "id": "test_dual_rsa::@6890427a1f51a3e7e1df", "jsonFile": "target-test_dual_rsa-Debug-4b5523d5127ddcb8961b.json", "name": "test_dual_rsa", "projectIndex": 0}]}], "kind": "codemodel", "paths": {"build": "F:/Projects/DualKeyEncryption/build", "source": "F:/Projects/DualKeyEncryption"}, "version": {"major": 2, "minor": 7}}