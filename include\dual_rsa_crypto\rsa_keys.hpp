#pragma once

#include <memory>
#include <vector>
#include <string>
#include <stdexcept>
#include <cstdint>

// 前向声明OpenSSL类型
struct evp_pkey_st;
typedef struct evp_pkey_st EVP_PKEY;
struct bignum_st;
typedef struct bignum_st BIGNUM;

namespace dual_rsa_crypto {

/**
 * @brief RSA密钥相关异常类
 */
class RSAKeyException : public std::runtime_error {
public:
    explicit RSAKeyException(const std::string& message) 
        : std::runtime_error(message) {}
};

/**
 * @brief RSA公钥类
 * 
 * 封装OpenSSL的EVP_PKEY，提供RSA公钥操作
 */
class RSAPublicKey {
public:
    /**
     * @brief 构造函数
     * @param key_size 密钥长度（位）
     */
    explicit RSAPublicKey(int key_size = 2048);
    
    /**
     * @brief 从PEM格式字符串构造
     * @param pem_string PEM格式的公钥字符串
     */
    explicit RSAPublicKey(const std::string& pem_string);
    
    /**
     * @brief 从EVP_PKEY构造（内部使用）
     * @param pkey OpenSSL EVP_PKEY指针
     */
    explicit RSAPublicKey(EVP_PKEY* pkey);
    
    // 禁用拷贝构造和赋值
    RSAPublicKey(const RSAPublicKey&) = delete;
    RSAPublicKey& operator=(const RSAPublicKey&) = delete;
    
    // 启用移动构造和赋值
    RSAPublicKey(RSAPublicKey&& other) noexcept;
    RSAPublicKey& operator=(RSAPublicKey&& other) noexcept;
    
    /**
     * @brief 析构函数
     */
    ~RSAPublicKey();
    
    /**
     * @brief 获取密钥长度（位）
     * @return 密钥长度
     */
    int getKeySize() const;
    
    /**
     * @brief 获取模数N
     * @return 模数的字节表示
     */
    std::vector<uint8_t> getModulus() const;
    
    /**
     * @brief 获取公钥指数e
     * @return 公钥指数的字节表示
     */
    std::vector<uint8_t> getPublicExponent() const;
    
    /**
     * @brief 导出为PEM格式字符串
     * @return PEM格式的公钥字符串
     */
    std::string toPEM() const;
    
    /**
     * @brief 获取内部EVP_PKEY指针（内部使用）
     * @return EVP_PKEY指针
     */
    EVP_PKEY* getEVP_PKEY() const;

private:
    class Impl;
    std::unique_ptr<Impl> pImpl;
};

/**
 * @brief RSA私钥类
 * 
 * 封装OpenSSL的EVP_PKEY，提供RSA私钥操作
 */
class RSAPrivateKey {
public:
    /**
     * @brief 构造函数
     * @param key_size 密钥长度（位）
     */
    explicit RSAPrivateKey(int key_size = 2048);
    
    /**
     * @brief 从PEM格式字符串构造
     * @param pem_string PEM格式的私钥字符串
     * @param password 私钥密码（可选）
     */
    explicit RSAPrivateKey(const std::string& pem_string, 
                          const std::string& password = "");
    
    /**
     * @brief 从EVP_PKEY构造（内部使用）
     * @param pkey OpenSSL EVP_PKEY指针
     */
    explicit RSAPrivateKey(EVP_PKEY* pkey);
    
    // 禁用拷贝构造和赋值
    RSAPrivateKey(const RSAPrivateKey&) = delete;
    RSAPrivateKey& operator=(const RSAPrivateKey&) = delete;
    
    // 启用移动构造和赋值
    RSAPrivateKey(RSAPrivateKey&& other) noexcept;
    RSAPrivateKey& operator=(RSAPrivateKey&& other) noexcept;
    
    /**
     * @brief 析构函数
     */
    ~RSAPrivateKey();
    
    /**
     * @brief 获取密钥长度（位）
     * @return 密钥长度
     */
    int getKeySize() const;
    
    /**
     * @brief 获取模数N
     * @return 模数的字节表示
     */
    std::vector<uint8_t> getModulus() const;
    
    /**
     * @brief 获取私钥指数d
     * @return 私钥指数的字节表示
     */
    std::vector<uint8_t> getPrivateExponent() const;
    
    /**
     * @brief 获取对应的公钥
     * @return RSA公钥对象
     */
    RSAPublicKey getPublicKey() const;
    
    /**
     * @brief 导出为PEM格式字符串
     * @param password 加密密码（可选）
     * @return PEM格式的私钥字符串
     */
    std::string toPEM(const std::string& password = "") const;
    
    /**
     * @brief 获取内部EVP_PKEY指针（内部使用）
     * @return EVP_PKEY指针
     */
    EVP_PKEY* getEVP_PKEY() const;

private:
    class Impl;
    std::unique_ptr<Impl> pImpl;
};

/**
 * @brief 生成RSA密钥对
 * @param key_size 密钥长度（位）
 * @return 包含私钥和公钥的pair
 */
std::pair<RSAPrivateKey, RSAPublicKey> generateRSAKeyPair(int key_size = 2048);

/**
 * @brief 检查两个模数是否互质
 * @param modulus1 第一个模数
 * @param modulus2 第二个模数
 * @return 如果互质返回true，否则返回false
 */
bool areModuliCoprime(const std::vector<uint8_t>& modulus1, 
                      const std::vector<uint8_t>& modulus2);

} // namespace dual_rsa_crypto
