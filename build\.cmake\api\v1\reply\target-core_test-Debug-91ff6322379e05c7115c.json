{"artifacts": [{"path": "core_test.exe"}, {"path": "core_test.pdb"}], "backtrace": 1, "backtraceGraph": {"commands": ["add_executable", "target_link_libraries", "include_directories"], "files": ["CMakeLists.txt"], "nodes": [{"file": 0}, {"command": 0, "file": 0, "line": 68, "parent": 0}, {"command": 1, "file": 0, "line": 69, "parent": 0}, {"command": 1, "file": 0, "line": 41, "parent": 0}, {"command": 2, "file": 0, "line": 28, "parent": 0}]}, "compileGroups": [{"compileCommandFragments": [{"fragment": " -Wall -Wextra -Wpedantic -g -O0 -std=c++17"}], "includes": [{"backtrace": 4, "path": "F:/Projects/DualKeyEncryption/include"}], "language": "CXX", "languageStandard": {"backtraces": [1], "standard": "17"}, "sourceIndexes": [0]}], "dependencies": [{"backtrace": 2, "id": "dual_rsa_crypto::@6890427a1f51a3e7e1df"}], "id": "core_test::@6890427a1f51a3e7e1df", "link": {"commandFragments": [{"fragment": "-Wall -Wextra -Wpedantic -g -O0", "role": "flags"}, {"fragment": "", "role": "flags"}, {"backtrace": 2, "fragment": "libdual_rsa_crypto.a", "role": "libraries"}, {"backtrace": 3, "fragment": "D:\\msys64\\mingw64\\lib\\libssl.dll.a", "role": "libraries"}, {"backtrace": 3, "fragment": "D:\\msys64\\mingw64\\lib\\libcrypto.dll.a", "role": "libraries"}, {"fragment": "-lkernel32 -luser32 -lgdi32 -lwinspool -lshell32 -lole32 -loleaut32 -luuid -lcomdlg32 -ladvapi32", "role": "libraries"}], "language": "CXX"}, "name": "core_test", "nameOnDisk": "core_test.exe", "paths": {"build": ".", "source": "."}, "sourceGroups": [{"name": "Source Files", "sourceIndexes": [0]}], "sources": [{"backtrace": 1, "compileGroupIndex": 0, "path": "tests/core_test.cpp", "sourceGroupIndex": 0}], "type": "EXECUTABLE"}