# CMAKE generated file: DO NOT EDIT!
# Generated by "MinGW Makefiles" Generator, CMake Version 3.30

# Default target executed when no arguments are given to make.
default_target: all
.PHONY : default_target

# Allow only one "make -f Makefile2" at a time, but pass parallelism.
.NOTPARALLEL:

#=============================================================================
# Special targets provided by cmake.

# Disable implicit rules so canonical targets will work.
.SUFFIXES:

# Disable VCS-based implicit rules.
% : %,v

# Disable VCS-based implicit rules.
% : RCS/%

# Disable VCS-based implicit rules.
% : RCS/%,v

# Disable VCS-based implicit rules.
% : SCCS/s.%

# Disable VCS-based implicit rules.
% : s.%

.SUFFIXES: .hpux_make_needs_suffix_list

# Command-line flag to silence nested $(MAKE).
$(VERBOSE)MAKESILENT = -s

#Suppress display of executed commands.
$(VERBOSE).SILENT:

# A target that is always out of date.
cmake_force:
.PHONY : cmake_force

#=============================================================================
# Set environment variables for the build.

SHELL = cmd.exe

# The CMake executable.
CMAKE_COMMAND = E:\cmake\bin\cmake.exe

# The command to remove a file.
RM = E:\cmake\bin\cmake.exe -E rm -f

# Escaping for special characters.
EQUALS = =

# The top-level source directory on which CMake was run.
CMAKE_SOURCE_DIR = F:\Projects\DualKeyEncryption

# The top-level build directory on which CMake was run.
CMAKE_BINARY_DIR = F:\Projects\DualKeyEncryption\build

#=============================================================================
# Targets provided globally by CMake.

# Special rule for the target test
test:
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --cyan "Running tests..."
	E:\cmake\bin\ctest.exe --force-new-ctest-process $(ARGS)
.PHONY : test

# Special rule for the target test
test/fast: test
.PHONY : test/fast

# Special rule for the target edit_cache
edit_cache:
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --cyan "Running CMake cache editor..."
	E:\cmake\bin\cmake-gui.exe -S$(CMAKE_SOURCE_DIR) -B$(CMAKE_BINARY_DIR)
.PHONY : edit_cache

# Special rule for the target edit_cache
edit_cache/fast: edit_cache
.PHONY : edit_cache/fast

# Special rule for the target rebuild_cache
rebuild_cache:
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --cyan "Running CMake to regenerate build system..."
	E:\cmake\bin\cmake.exe --regenerate-during-build -S$(CMAKE_SOURCE_DIR) -B$(CMAKE_BINARY_DIR)
.PHONY : rebuild_cache

# Special rule for the target rebuild_cache
rebuild_cache/fast: rebuild_cache
.PHONY : rebuild_cache/fast

# Special rule for the target list_install_components
list_install_components:
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --cyan "Available install components are: \"Unspecified\""
.PHONY : list_install_components

# Special rule for the target list_install_components
list_install_components/fast: list_install_components
.PHONY : list_install_components/fast

# Special rule for the target install
install: preinstall
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --cyan "Install the project..."
	E:\cmake\bin\cmake.exe -P cmake_install.cmake
.PHONY : install

# Special rule for the target install
install/fast: preinstall/fast
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --cyan "Install the project..."
	E:\cmake\bin\cmake.exe -P cmake_install.cmake
.PHONY : install/fast

# Special rule for the target install/local
install/local: preinstall
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --cyan "Installing only the local directory..."
	E:\cmake\bin\cmake.exe -DCMAKE_INSTALL_LOCAL_ONLY=1 -P cmake_install.cmake
.PHONY : install/local

# Special rule for the target install/local
install/local/fast: preinstall/fast
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --cyan "Installing only the local directory..."
	E:\cmake\bin\cmake.exe -DCMAKE_INSTALL_LOCAL_ONLY=1 -P cmake_install.cmake
.PHONY : install/local/fast

# Special rule for the target install/strip
install/strip: preinstall
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --cyan "Installing the project stripped..."
	E:\cmake\bin\cmake.exe -DCMAKE_INSTALL_DO_STRIP=1 -P cmake_install.cmake
.PHONY : install/strip

# Special rule for the target install/strip
install/strip/fast: preinstall/fast
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --cyan "Installing the project stripped..."
	E:\cmake\bin\cmake.exe -DCMAKE_INSTALL_DO_STRIP=1 -P cmake_install.cmake
.PHONY : install/strip/fast

# The main all target
all: cmake_check_build_system
	$(CMAKE_COMMAND) -E cmake_progress_start F:\Projects\DualKeyEncryption\build\CMakeFiles F:\Projects\DualKeyEncryption\build\\CMakeFiles\progress.marks
	$(MAKE) $(MAKESILENT) -f CMakeFiles\Makefile2 all
	$(CMAKE_COMMAND) -E cmake_progress_start F:\Projects\DualKeyEncryption\build\CMakeFiles 0
.PHONY : all

# The main clean target
clean:
	$(MAKE) $(MAKESILENT) -f CMakeFiles\Makefile2 clean
.PHONY : clean

# The main clean target
clean/fast: clean
.PHONY : clean/fast

# Prepare targets for installation.
preinstall: all
	$(MAKE) $(MAKESILENT) -f CMakeFiles\Makefile2 preinstall
.PHONY : preinstall

# Prepare targets for installation.
preinstall/fast:
	$(MAKE) $(MAKESILENT) -f CMakeFiles\Makefile2 preinstall
.PHONY : preinstall/fast

# clear depends
depend:
	$(CMAKE_COMMAND) -S$(CMAKE_SOURCE_DIR) -B$(CMAKE_BINARY_DIR) --check-build-system CMakeFiles\Makefile.cmake 1
.PHONY : depend

#=============================================================================
# Target rules for targets named dual_rsa_crypto

# Build rule for target.
dual_rsa_crypto: cmake_check_build_system
	$(MAKE) $(MAKESILENT) -f CMakeFiles\Makefile2 dual_rsa_crypto
.PHONY : dual_rsa_crypto

# fast build rule for target.
dual_rsa_crypto/fast:
	$(MAKE) $(MAKESILENT) -f CMakeFiles\dual_rsa_crypto.dir\build.make CMakeFiles/dual_rsa_crypto.dir/build
.PHONY : dual_rsa_crypto/fast

#=============================================================================
# Target rules for targets named test_dual_rsa

# Build rule for target.
test_dual_rsa: cmake_check_build_system
	$(MAKE) $(MAKESILENT) -f CMakeFiles\Makefile2 test_dual_rsa
.PHONY : test_dual_rsa

# fast build rule for target.
test_dual_rsa/fast:
	$(MAKE) $(MAKESILENT) -f CMakeFiles\test_dual_rsa.dir\build.make CMakeFiles/test_dual_rsa.dir/build
.PHONY : test_dual_rsa/fast

#=============================================================================
# Target rules for targets named simple_test

# Build rule for target.
simple_test: cmake_check_build_system
	$(MAKE) $(MAKESILENT) -f CMakeFiles\Makefile2 simple_test
.PHONY : simple_test

# fast build rule for target.
simple_test/fast:
	$(MAKE) $(MAKESILENT) -f CMakeFiles\simple_test.dir\build.make CMakeFiles/simple_test.dir/build
.PHONY : simple_test/fast

#=============================================================================
# Target rules for targets named debug_test

# Build rule for target.
debug_test: cmake_check_build_system
	$(MAKE) $(MAKESILENT) -f CMakeFiles\Makefile2 debug_test
.PHONY : debug_test

# fast build rule for target.
debug_test/fast:
	$(MAKE) $(MAKESILENT) -f CMakeFiles\debug_test.dir\build.make CMakeFiles/debug_test.dir/build
.PHONY : debug_test/fast

#=============================================================================
# Target rules for targets named core_test

# Build rule for target.
core_test: cmake_check_build_system
	$(MAKE) $(MAKESILENT) -f CMakeFiles\Makefile2 core_test
.PHONY : core_test

# fast build rule for target.
core_test/fast:
	$(MAKE) $(MAKESILENT) -f CMakeFiles\core_test.dir\build.make CMakeFiles/core_test.dir/build
.PHONY : core_test/fast

#=============================================================================
# Target rules for targets named api_compatibility_test

# Build rule for target.
api_compatibility_test: cmake_check_build_system
	$(MAKE) $(MAKESILENT) -f CMakeFiles\Makefile2 api_compatibility_test
.PHONY : api_compatibility_test

# fast build rule for target.
api_compatibility_test/fast:
	$(MAKE) $(MAKESILENT) -f CMakeFiles\api_compatibility_test.dir\build.make CMakeFiles/api_compatibility_test.dir/build
.PHONY : api_compatibility_test/fast

src/crt_solver.obj: src/crt_solver.cpp.obj
.PHONY : src/crt_solver.obj

# target to build an object file
src/crt_solver.cpp.obj:
	$(MAKE) $(MAKESILENT) -f CMakeFiles\dual_rsa_crypto.dir\build.make CMakeFiles/dual_rsa_crypto.dir/src/crt_solver.cpp.obj
.PHONY : src/crt_solver.cpp.obj

src/crt_solver.i: src/crt_solver.cpp.i
.PHONY : src/crt_solver.i

# target to preprocess a source file
src/crt_solver.cpp.i:
	$(MAKE) $(MAKESILENT) -f CMakeFiles\dual_rsa_crypto.dir\build.make CMakeFiles/dual_rsa_crypto.dir/src/crt_solver.cpp.i
.PHONY : src/crt_solver.cpp.i

src/crt_solver.s: src/crt_solver.cpp.s
.PHONY : src/crt_solver.s

# target to generate assembly for a file
src/crt_solver.cpp.s:
	$(MAKE) $(MAKESILENT) -f CMakeFiles\dual_rsa_crypto.dir\build.make CMakeFiles/dual_rsa_crypto.dir/src/crt_solver.cpp.s
.PHONY : src/crt_solver.cpp.s

src/dual_rsa_crypto.obj: src/dual_rsa_crypto.cpp.obj
.PHONY : src/dual_rsa_crypto.obj

# target to build an object file
src/dual_rsa_crypto.cpp.obj:
	$(MAKE) $(MAKESILENT) -f CMakeFiles\dual_rsa_crypto.dir\build.make CMakeFiles/dual_rsa_crypto.dir/src/dual_rsa_crypto.cpp.obj
.PHONY : src/dual_rsa_crypto.cpp.obj

src/dual_rsa_crypto.i: src/dual_rsa_crypto.cpp.i
.PHONY : src/dual_rsa_crypto.i

# target to preprocess a source file
src/dual_rsa_crypto.cpp.i:
	$(MAKE) $(MAKESILENT) -f CMakeFiles\dual_rsa_crypto.dir\build.make CMakeFiles/dual_rsa_crypto.dir/src/dual_rsa_crypto.cpp.i
.PHONY : src/dual_rsa_crypto.cpp.i

src/dual_rsa_crypto.s: src/dual_rsa_crypto.cpp.s
.PHONY : src/dual_rsa_crypto.s

# target to generate assembly for a file
src/dual_rsa_crypto.cpp.s:
	$(MAKE) $(MAKESILENT) -f CMakeFiles\dual_rsa_crypto.dir\build.make CMakeFiles/dual_rsa_crypto.dir/src/dual_rsa_crypto.cpp.s
.PHONY : src/dual_rsa_crypto.cpp.s

src/rsa_keys.obj: src/rsa_keys.cpp.obj
.PHONY : src/rsa_keys.obj

# target to build an object file
src/rsa_keys.cpp.obj:
	$(MAKE) $(MAKESILENT) -f CMakeFiles\dual_rsa_crypto.dir\build.make CMakeFiles/dual_rsa_crypto.dir/src/rsa_keys.cpp.obj
.PHONY : src/rsa_keys.cpp.obj

src/rsa_keys.i: src/rsa_keys.cpp.i
.PHONY : src/rsa_keys.i

# target to preprocess a source file
src/rsa_keys.cpp.i:
	$(MAKE) $(MAKESILENT) -f CMakeFiles\dual_rsa_crypto.dir\build.make CMakeFiles/dual_rsa_crypto.dir/src/rsa_keys.cpp.i
.PHONY : src/rsa_keys.cpp.i

src/rsa_keys.s: src/rsa_keys.cpp.s
.PHONY : src/rsa_keys.s

# target to generate assembly for a file
src/rsa_keys.cpp.s:
	$(MAKE) $(MAKESILENT) -f CMakeFiles\dual_rsa_crypto.dir\build.make CMakeFiles/dual_rsa_crypto.dir/src/rsa_keys.cpp.s
.PHONY : src/rsa_keys.cpp.s

tests/api_compatibility_test.obj: tests/api_compatibility_test.cpp.obj
.PHONY : tests/api_compatibility_test.obj

# target to build an object file
tests/api_compatibility_test.cpp.obj:
	$(MAKE) $(MAKESILENT) -f CMakeFiles\api_compatibility_test.dir\build.make CMakeFiles/api_compatibility_test.dir/tests/api_compatibility_test.cpp.obj
.PHONY : tests/api_compatibility_test.cpp.obj

tests/api_compatibility_test.i: tests/api_compatibility_test.cpp.i
.PHONY : tests/api_compatibility_test.i

# target to preprocess a source file
tests/api_compatibility_test.cpp.i:
	$(MAKE) $(MAKESILENT) -f CMakeFiles\api_compatibility_test.dir\build.make CMakeFiles/api_compatibility_test.dir/tests/api_compatibility_test.cpp.i
.PHONY : tests/api_compatibility_test.cpp.i

tests/api_compatibility_test.s: tests/api_compatibility_test.cpp.s
.PHONY : tests/api_compatibility_test.s

# target to generate assembly for a file
tests/api_compatibility_test.cpp.s:
	$(MAKE) $(MAKESILENT) -f CMakeFiles\api_compatibility_test.dir\build.make CMakeFiles/api_compatibility_test.dir/tests/api_compatibility_test.cpp.s
.PHONY : tests/api_compatibility_test.cpp.s

tests/core_test.obj: tests/core_test.cpp.obj
.PHONY : tests/core_test.obj

# target to build an object file
tests/core_test.cpp.obj:
	$(MAKE) $(MAKESILENT) -f CMakeFiles\core_test.dir\build.make CMakeFiles/core_test.dir/tests/core_test.cpp.obj
.PHONY : tests/core_test.cpp.obj

tests/core_test.i: tests/core_test.cpp.i
.PHONY : tests/core_test.i

# target to preprocess a source file
tests/core_test.cpp.i:
	$(MAKE) $(MAKESILENT) -f CMakeFiles\core_test.dir\build.make CMakeFiles/core_test.dir/tests/core_test.cpp.i
.PHONY : tests/core_test.cpp.i

tests/core_test.s: tests/core_test.cpp.s
.PHONY : tests/core_test.s

# target to generate assembly for a file
tests/core_test.cpp.s:
	$(MAKE) $(MAKESILENT) -f CMakeFiles\core_test.dir\build.make CMakeFiles/core_test.dir/tests/core_test.cpp.s
.PHONY : tests/core_test.cpp.s

tests/debug_test.obj: tests/debug_test.cpp.obj
.PHONY : tests/debug_test.obj

# target to build an object file
tests/debug_test.cpp.obj:
	$(MAKE) $(MAKESILENT) -f CMakeFiles\debug_test.dir\build.make CMakeFiles/debug_test.dir/tests/debug_test.cpp.obj
.PHONY : tests/debug_test.cpp.obj

tests/debug_test.i: tests/debug_test.cpp.i
.PHONY : tests/debug_test.i

# target to preprocess a source file
tests/debug_test.cpp.i:
	$(MAKE) $(MAKESILENT) -f CMakeFiles\debug_test.dir\build.make CMakeFiles/debug_test.dir/tests/debug_test.cpp.i
.PHONY : tests/debug_test.cpp.i

tests/debug_test.s: tests/debug_test.cpp.s
.PHONY : tests/debug_test.s

# target to generate assembly for a file
tests/debug_test.cpp.s:
	$(MAKE) $(MAKESILENT) -f CMakeFiles\debug_test.dir\build.make CMakeFiles/debug_test.dir/tests/debug_test.cpp.s
.PHONY : tests/debug_test.cpp.s

tests/simple_test.obj: tests/simple_test.cpp.obj
.PHONY : tests/simple_test.obj

# target to build an object file
tests/simple_test.cpp.obj:
	$(MAKE) $(MAKESILENT) -f CMakeFiles\simple_test.dir\build.make CMakeFiles/simple_test.dir/tests/simple_test.cpp.obj
.PHONY : tests/simple_test.cpp.obj

tests/simple_test.i: tests/simple_test.cpp.i
.PHONY : tests/simple_test.i

# target to preprocess a source file
tests/simple_test.cpp.i:
	$(MAKE) $(MAKESILENT) -f CMakeFiles\simple_test.dir\build.make CMakeFiles/simple_test.dir/tests/simple_test.cpp.i
.PHONY : tests/simple_test.cpp.i

tests/simple_test.s: tests/simple_test.cpp.s
.PHONY : tests/simple_test.s

# target to generate assembly for a file
tests/simple_test.cpp.s:
	$(MAKE) $(MAKESILENT) -f CMakeFiles\simple_test.dir\build.make CMakeFiles/simple_test.dir/tests/simple_test.cpp.s
.PHONY : tests/simple_test.cpp.s

tests/test_dual_rsa.obj: tests/test_dual_rsa.cpp.obj
.PHONY : tests/test_dual_rsa.obj

# target to build an object file
tests/test_dual_rsa.cpp.obj:
	$(MAKE) $(MAKESILENT) -f CMakeFiles\test_dual_rsa.dir\build.make CMakeFiles/test_dual_rsa.dir/tests/test_dual_rsa.cpp.obj
.PHONY : tests/test_dual_rsa.cpp.obj

tests/test_dual_rsa.i: tests/test_dual_rsa.cpp.i
.PHONY : tests/test_dual_rsa.i

# target to preprocess a source file
tests/test_dual_rsa.cpp.i:
	$(MAKE) $(MAKESILENT) -f CMakeFiles\test_dual_rsa.dir\build.make CMakeFiles/test_dual_rsa.dir/tests/test_dual_rsa.cpp.i
.PHONY : tests/test_dual_rsa.cpp.i

tests/test_dual_rsa.s: tests/test_dual_rsa.cpp.s
.PHONY : tests/test_dual_rsa.s

# target to generate assembly for a file
tests/test_dual_rsa.cpp.s:
	$(MAKE) $(MAKESILENT) -f CMakeFiles\test_dual_rsa.dir\build.make CMakeFiles/test_dual_rsa.dir/tests/test_dual_rsa.cpp.s
.PHONY : tests/test_dual_rsa.cpp.s

# Help Target
help:
	@echo The following are some of the valid targets for this Makefile:
	@echo ... all (the default if no target is provided)
	@echo ... clean
	@echo ... depend
	@echo ... edit_cache
	@echo ... install
	@echo ... install/local
	@echo ... install/strip
	@echo ... list_install_components
	@echo ... rebuild_cache
	@echo ... test
	@echo ... api_compatibility_test
	@echo ... core_test
	@echo ... debug_test
	@echo ... dual_rsa_crypto
	@echo ... simple_test
	@echo ... test_dual_rsa
	@echo ... src/crt_solver.obj
	@echo ... src/crt_solver.i
	@echo ... src/crt_solver.s
	@echo ... src/dual_rsa_crypto.obj
	@echo ... src/dual_rsa_crypto.i
	@echo ... src/dual_rsa_crypto.s
	@echo ... src/rsa_keys.obj
	@echo ... src/rsa_keys.i
	@echo ... src/rsa_keys.s
	@echo ... tests/api_compatibility_test.obj
	@echo ... tests/api_compatibility_test.i
	@echo ... tests/api_compatibility_test.s
	@echo ... tests/core_test.obj
	@echo ... tests/core_test.i
	@echo ... tests/core_test.s
	@echo ... tests/debug_test.obj
	@echo ... tests/debug_test.i
	@echo ... tests/debug_test.s
	@echo ... tests/simple_test.obj
	@echo ... tests/simple_test.i
	@echo ... tests/simple_test.s
	@echo ... tests/test_dual_rsa.obj
	@echo ... tests/test_dual_rsa.i
	@echo ... tests/test_dual_rsa.s
.PHONY : help



#=============================================================================
# Special targets to cleanup operation of make.

# Special rule to run CMake to check the build system integrity.
# No rule that depends on this can have commands that come from listfiles
# because they might be regenerated.
cmake_check_build_system:
	$(CMAKE_COMMAND) -S$(CMAKE_SOURCE_DIR) -B$(CMAKE_BINARY_DIR) --check-build-system CMakeFiles\Makefile.cmake 0
.PHONY : cmake_check_build_system

