# CMAKE generated file: DO NOT EDIT!
# Generated by "MinGW Makefiles" Generator, CMake Version 3.30

# Delete rule output on recipe failure.
.DELETE_ON_ERROR:

#=============================================================================
# Special targets provided by cmake.

# Disable implicit rules so canonical targets will work.
.SUFFIXES:

# Disable VCS-based implicit rules.
% : %,v

# Disable VCS-based implicit rules.
% : RCS/%

# Disable VCS-based implicit rules.
% : RCS/%,v

# Disable VCS-based implicit rules.
% : SCCS/s.%

# Disable VCS-based implicit rules.
% : s.%

.SUFFIXES: .hpux_make_needs_suffix_list

# Command-line flag to silence nested $(MAKE).
$(VERBOSE)MAKESILENT = -s

#Suppress display of executed commands.
$(VERBOSE).SILENT:

# A target that is always out of date.
cmake_force:
.PHONY : cmake_force

#=============================================================================
# Set environment variables for the build.

SHELL = cmd.exe

# The CMake executable.
CMAKE_COMMAND = E:\cmake\bin\cmake.exe

# The command to remove a file.
RM = E:\cmake\bin\cmake.exe -E rm -f

# Escaping for special characters.
EQUALS = =

# The top-level source directory on which CMake was run.
CMAKE_SOURCE_DIR = F:\Projects\DualKeyEncryption

# The top-level build directory on which CMake was run.
CMAKE_BINARY_DIR = F:\Projects\DualKeyEncryption\build

# Include any dependencies generated for this target.
include CMakeFiles/test_dual_rsa.dir/depend.make
# Include any dependencies generated by the compiler for this target.
include CMakeFiles/test_dual_rsa.dir/compiler_depend.make

# Include the progress variables for this target.
include CMakeFiles/test_dual_rsa.dir/progress.make

# Include the compile flags for this target's objects.
include CMakeFiles/test_dual_rsa.dir/flags.make

CMakeFiles/test_dual_rsa.dir/tests/test_dual_rsa.cpp.obj: CMakeFiles/test_dual_rsa.dir/flags.make
CMakeFiles/test_dual_rsa.dir/tests/test_dual_rsa.cpp.obj: CMakeFiles/test_dual_rsa.dir/includes_CXX.rsp
CMakeFiles/test_dual_rsa.dir/tests/test_dual_rsa.cpp.obj: F:/Projects/DualKeyEncryption/tests/test_dual_rsa.cpp
CMakeFiles/test_dual_rsa.dir/tests/test_dual_rsa.cpp.obj: CMakeFiles/test_dual_rsa.dir/compiler_depend.ts
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green --progress-dir=F:\Projects\DualKeyEncryption\build\CMakeFiles --progress-num=$(CMAKE_PROGRESS_1) "Building CXX object CMakeFiles/test_dual_rsa.dir/tests/test_dual_rsa.cpp.obj"
	D:\msys64\mingw64\bin\g++.exe $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -MD -MT CMakeFiles/test_dual_rsa.dir/tests/test_dual_rsa.cpp.obj -MF CMakeFiles\test_dual_rsa.dir\tests\test_dual_rsa.cpp.obj.d -o CMakeFiles\test_dual_rsa.dir\tests\test_dual_rsa.cpp.obj -c F:\Projects\DualKeyEncryption\tests\test_dual_rsa.cpp

CMakeFiles/test_dual_rsa.dir/tests/test_dual_rsa.cpp.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Preprocessing CXX source to CMakeFiles/test_dual_rsa.dir/tests/test_dual_rsa.cpp.i"
	D:\msys64\mingw64\bin\g++.exe $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -E F:\Projects\DualKeyEncryption\tests\test_dual_rsa.cpp > CMakeFiles\test_dual_rsa.dir\tests\test_dual_rsa.cpp.i

CMakeFiles/test_dual_rsa.dir/tests/test_dual_rsa.cpp.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Compiling CXX source to assembly CMakeFiles/test_dual_rsa.dir/tests/test_dual_rsa.cpp.s"
	D:\msys64\mingw64\bin\g++.exe $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -S F:\Projects\DualKeyEncryption\tests\test_dual_rsa.cpp -o CMakeFiles\test_dual_rsa.dir\tests\test_dual_rsa.cpp.s

# Object files for target test_dual_rsa
test_dual_rsa_OBJECTS = \
"CMakeFiles/test_dual_rsa.dir/tests/test_dual_rsa.cpp.obj"

# External object files for target test_dual_rsa
test_dual_rsa_EXTERNAL_OBJECTS =

test_dual_rsa.exe: CMakeFiles/test_dual_rsa.dir/tests/test_dual_rsa.cpp.obj
test_dual_rsa.exe: CMakeFiles/test_dual_rsa.dir/build.make
test_dual_rsa.exe: libdual_rsa_crypto.a
test_dual_rsa.exe: D:/msys64/mingw64/lib/libssl.dll.a
test_dual_rsa.exe: D:/msys64/mingw64/lib/libcrypto.dll.a
test_dual_rsa.exe: CMakeFiles/test_dual_rsa.dir/linkLibs.rsp
test_dual_rsa.exe: CMakeFiles/test_dual_rsa.dir/objects1.rsp
test_dual_rsa.exe: CMakeFiles/test_dual_rsa.dir/link.txt
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green --bold --progress-dir=F:\Projects\DualKeyEncryption\build\CMakeFiles --progress-num=$(CMAKE_PROGRESS_2) "Linking CXX executable test_dual_rsa.exe"
	$(CMAKE_COMMAND) -E cmake_link_script CMakeFiles\test_dual_rsa.dir\link.txt --verbose=$(VERBOSE)

# Rule to build all files generated by this target.
CMakeFiles/test_dual_rsa.dir/build: test_dual_rsa.exe
.PHONY : CMakeFiles/test_dual_rsa.dir/build

CMakeFiles/test_dual_rsa.dir/clean:
	$(CMAKE_COMMAND) -P CMakeFiles\test_dual_rsa.dir\cmake_clean.cmake
.PHONY : CMakeFiles/test_dual_rsa.dir/clean

CMakeFiles/test_dual_rsa.dir/depend:
	$(CMAKE_COMMAND) -E cmake_depends "MinGW Makefiles" F:\Projects\DualKeyEncryption F:\Projects\DualKeyEncryption F:\Projects\DualKeyEncryption\build F:\Projects\DualKeyEncryption\build F:\Projects\DualKeyEncryption\build\CMakeFiles\test_dual_rsa.dir\DependInfo.cmake "--color=$(COLOR)"
.PHONY : CMakeFiles/test_dual_rsa.dir/depend

