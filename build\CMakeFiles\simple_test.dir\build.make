# CMAKE generated file: DO NOT EDIT!
# Generated by "MinGW Makefiles" Generator, CMake Version 3.30

# Delete rule output on recipe failure.
.DELETE_ON_ERROR:

#=============================================================================
# Special targets provided by cmake.

# Disable implicit rules so canonical targets will work.
.SUFFIXES:

# Disable VCS-based implicit rules.
% : %,v

# Disable VCS-based implicit rules.
% : RCS/%

# Disable VCS-based implicit rules.
% : RCS/%,v

# Disable VCS-based implicit rules.
% : SCCS/s.%

# Disable VCS-based implicit rules.
% : s.%

.SUFFIXES: .hpux_make_needs_suffix_list

# Command-line flag to silence nested $(MAKE).
$(VERBOSE)MAKESILENT = -s

#Suppress display of executed commands.
$(VERBOSE).SILENT:

# A target that is always out of date.
cmake_force:
.PHONY : cmake_force

#=============================================================================
# Set environment variables for the build.

SHELL = cmd.exe

# The CMake executable.
CMAKE_COMMAND = E:\cmake\bin\cmake.exe

# The command to remove a file.
RM = E:\cmake\bin\cmake.exe -E rm -f

# Escaping for special characters.
EQUALS = =

# The top-level source directory on which CMake was run.
CMAKE_SOURCE_DIR = F:\Projects\DualKeyEncryption

# The top-level build directory on which CMake was run.
CMAKE_BINARY_DIR = F:\Projects\DualKeyEncryption\build

# Include any dependencies generated for this target.
include CMakeFiles/simple_test.dir/depend.make
# Include any dependencies generated by the compiler for this target.
include CMakeFiles/simple_test.dir/compiler_depend.make

# Include the progress variables for this target.
include CMakeFiles/simple_test.dir/progress.make

# Include the compile flags for this target's objects.
include CMakeFiles/simple_test.dir/flags.make

CMakeFiles/simple_test.dir/tests/simple_test.cpp.obj: CMakeFiles/simple_test.dir/flags.make
CMakeFiles/simple_test.dir/tests/simple_test.cpp.obj: CMakeFiles/simple_test.dir/includes_CXX.rsp
CMakeFiles/simple_test.dir/tests/simple_test.cpp.obj: F:/Projects/DualKeyEncryption/tests/simple_test.cpp
CMakeFiles/simple_test.dir/tests/simple_test.cpp.obj: CMakeFiles/simple_test.dir/compiler_depend.ts
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green --progress-dir=F:\Projects\DualKeyEncryption\build\CMakeFiles --progress-num=$(CMAKE_PROGRESS_1) "Building CXX object CMakeFiles/simple_test.dir/tests/simple_test.cpp.obj"
	D:\msys64\mingw64\bin\g++.exe $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -MD -MT CMakeFiles/simple_test.dir/tests/simple_test.cpp.obj -MF CMakeFiles\simple_test.dir\tests\simple_test.cpp.obj.d -o CMakeFiles\simple_test.dir\tests\simple_test.cpp.obj -c F:\Projects\DualKeyEncryption\tests\simple_test.cpp

CMakeFiles/simple_test.dir/tests/simple_test.cpp.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Preprocessing CXX source to CMakeFiles/simple_test.dir/tests/simple_test.cpp.i"
	D:\msys64\mingw64\bin\g++.exe $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -E F:\Projects\DualKeyEncryption\tests\simple_test.cpp > CMakeFiles\simple_test.dir\tests\simple_test.cpp.i

CMakeFiles/simple_test.dir/tests/simple_test.cpp.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Compiling CXX source to assembly CMakeFiles/simple_test.dir/tests/simple_test.cpp.s"
	D:\msys64\mingw64\bin\g++.exe $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -S F:\Projects\DualKeyEncryption\tests\simple_test.cpp -o CMakeFiles\simple_test.dir\tests\simple_test.cpp.s

# Object files for target simple_test
simple_test_OBJECTS = \
"CMakeFiles/simple_test.dir/tests/simple_test.cpp.obj"

# External object files for target simple_test
simple_test_EXTERNAL_OBJECTS =

simple_test.exe: CMakeFiles/simple_test.dir/tests/simple_test.cpp.obj
simple_test.exe: CMakeFiles/simple_test.dir/build.make
simple_test.exe: libdual_rsa_crypto.a
simple_test.exe: D:/msys64/mingw64/lib/libssl.dll.a
simple_test.exe: D:/msys64/mingw64/lib/libcrypto.dll.a
simple_test.exe: CMakeFiles/simple_test.dir/linkLibs.rsp
simple_test.exe: CMakeFiles/simple_test.dir/objects1.rsp
simple_test.exe: CMakeFiles/simple_test.dir/link.txt
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green --bold --progress-dir=F:\Projects\DualKeyEncryption\build\CMakeFiles --progress-num=$(CMAKE_PROGRESS_2) "Linking CXX executable simple_test.exe"
	$(CMAKE_COMMAND) -E cmake_link_script CMakeFiles\simple_test.dir\link.txt --verbose=$(VERBOSE)

# Rule to build all files generated by this target.
CMakeFiles/simple_test.dir/build: simple_test.exe
.PHONY : CMakeFiles/simple_test.dir/build

CMakeFiles/simple_test.dir/clean:
	$(CMAKE_COMMAND) -P CMakeFiles\simple_test.dir\cmake_clean.cmake
.PHONY : CMakeFiles/simple_test.dir/clean

CMakeFiles/simple_test.dir/depend:
	$(CMAKE_COMMAND) -E cmake_depends "MinGW Makefiles" F:\Projects\DualKeyEncryption F:\Projects\DualKeyEncryption F:\Projects\DualKeyEncryption\build F:\Projects\DualKeyEncryption\build F:\Projects\DualKeyEncryption\build\CMakeFiles\simple_test.dir\DependInfo.cmake "--color=$(COLOR)"
.PHONY : CMakeFiles/simple_test.dir/depend

