#include "dual_rsa_crypto/dual_rsa_crypto.hpp"
#include <iostream>
#include <string>

using namespace dual_rsa_crypto;

int main() {
    try {
        std::cout << "开始简单测试..." << std::endl;
        
        // 生成密钥对
        std::cout << "生成第一个密钥对..." << std::endl;
        auto [private_key1, public_key1] = generateRSAKeyPair(1024);
        std::cout << "第一个密钥对生成成功" << std::endl;
        
        std::cout << "生成第二个密钥对..." << std::endl;
        auto [private_key2, public_key2] = generateRSAKeyPair(1024);
        std::cout << "第二个密钥对生成成功" << std::endl;
        
        // 验证密钥大小
        std::cout << "密钥1大小: " << public_key1.getKeySize() << " 位" << std::endl;
        std::cout << "密钥2大小: " << public_key2.getKeySize() << " 位" << std::endl;
        
        // 测试基本加密解密
        std::string message1 = "Hello World 1";
        std::string message2 = "Hello World 2";
        
        std::vector<uint8_t> plaintext1(message1.begin(), message1.end());
        std::vector<uint8_t> plaintext2(message2.begin(), message2.end());
        
        std::cout << "开始双密钥加密..." << std::endl;
        auto ciphertext = encrypt_dual(plaintext1, plaintext2, public_key1, public_key2);
        std::cout << "双密钥加密成功，密文长度: " << ciphertext.size() << " 字节" << std::endl;
        
        std::cout << "开始解密..." << std::endl;
        auto decrypted1 = decrypt_single(ciphertext, private_key1);
        auto decrypted2 = decrypt_single(ciphertext, private_key2);
        
        std::string recovered1(decrypted1.begin(), decrypted1.end());
        std::string recovered2(decrypted2.begin(), decrypted2.end());
        
        std::cout << "解密结果1: " << recovered1 << std::endl;
        std::cout << "解密结果2: " << recovered2 << std::endl;
        
        if (recovered1 == message1 && recovered2 == message2) {
            std::cout << "✓ 测试成功!" << std::endl;
            return 0;
        } else {
            std::cout << "✗ 测试失败!" << std::endl;
            return 1;
        }
        
    } catch (const std::exception& e) {
        std::cout << "测试异常: " << e.what() << std::endl;
        return 1;
    }
}
