#include "dual_rsa_crypto/dual_rsa_crypto.hpp"
#include <iostream>
#include <string>

using namespace dual_rsa_crypto;

int main() {
    try {
        std::cout << "Starting simple test..." << std::endl;

        // Generate key pairs
        std::cout << "Generating first key pair..." << std::endl;
        auto [private_key1, public_key1] = generateRSAKeyPair(1024);
        std::cout << "First key pair generated successfully" << std::endl;

        std::cout << "Generating second key pair..." << std::endl;
        auto [private_key2, public_key2] = generateRSAKeyPair(1024);
        std::cout << "Second key pair generated successfully" << std::endl;

        // Verify key sizes
        std::cout << "Key1 size: " << public_key1.getKeySize() << " bits" << std::endl;
        std::cout << "Key2 size: " << public_key2.getKeySize() << " bits" << std::endl;

        // Test basic encryption/decryption
        std::string message1 = "Hello World 1";
        std::string message2 = "Hello World 2";

        std::vector<uint8_t> plaintext1(message1.begin(), message1.end());
        std::vector<uint8_t> plaintext2(message2.begin(), message2.end());

        std::cout << "Starting dual-key encryption..." << std::endl;
        auto ciphertext = encrypt_dual(plaintext1, plaintext2, public_key1, public_key2);
        std::cout << "Dual-key encryption successful, ciphertext length: " << ciphertext.size() << " bytes" << std::endl;

        std::cout << "Starting decryption..." << std::endl;
        auto decrypted1 = decrypt_single(ciphertext, private_key1);
        auto decrypted2 = decrypt_single(ciphertext, private_key2);

        std::string recovered1(decrypted1.begin(), decrypted1.end());
        std::string recovered2(decrypted2.begin(), decrypted2.end());

        std::cout << "Decrypted message 1: " << recovered1 << std::endl;
        std::cout << "Decrypted message 2: " << recovered2 << std::endl;

        if (recovered1 == message1 && recovered2 == message2) {
            std::cout << "SUCCESS: Test passed!" << std::endl;
            return 0;
        } else {
            std::cout << "FAILURE: Test failed!" << std::endl;
            return 1;
        }

    } catch (const std::exception& e) {
        std::cout << "Test exception: " << e.what() << std::endl;
        return 1;
    }
}
