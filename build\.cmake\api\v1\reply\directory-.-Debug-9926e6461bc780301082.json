{"backtraceGraph": {"commands": ["install"], "files": ["CMakeLists.txt"], "nodes": [{"file": 0}, {"command": 0, "file": 0, "line": 77, "parent": 0}, {"command": 0, "file": 0, "line": 84, "parent": 0}, {"command": 0, "file": 0, "line": 87, "parent": 0}]}, "installers": [{"backtrace": 1, "component": "Unspecified", "destination": "lib", "paths": ["libdual_rsa_crypto.a"], "targetId": "dual_rsa_crypto::@6890427a1f51a3e7e1df", "targetIndex": 2, "type": "target"}, {"backtrace": 2, "component": "Unspecified", "destination": "include", "paths": [{"from": "include", "to": "."}], "type": "directory"}, {"backtrace": 3, "component": "Unspecified", "destination": "lib/cmake/DualRSACrypto", "exportName": "DualRSACryptoTargets", "exportTargets": [{"id": "dual_rsa_crypto::@6890427a1f51a3e7e1df", "index": 2}], "paths": ["CMakeFiles/Export/9e301c24bb911e74c3b8f5c4291a3f10/DualRSACryptoTargets.cmake"], "type": "export"}], "paths": {"build": ".", "source": "."}}