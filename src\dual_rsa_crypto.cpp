#include "dual_rsa_crypto/dual_rsa_crypto.hpp"

#include <openssl/evp.h>
#include <openssl/rsa.h>
#include <openssl/rand.h>
#include <openssl/err.h>
#include <openssl/bn.h>

#include <algorithm>
#include <cstring>

namespace dual_rsa_crypto {

namespace {
    // OpenSSL错误处理辅助函数
    std::string getOpenSSLError() {
        BIO* bio = BIO_new(BIO_s_mem());
        ERR_print_errors(bio);
        char* data;
        long len = BIO_get_mem_data(bio, &data);
        std::string result(data, len);
        BIO_free(bio);
        return result;
    }
    
    // 获取填充类型
    int getPaddingType(PaddingMode padding) {
        switch (padding) {
            case PaddingMode::PKCS1:
                return RSA_PKCS1_PADDING;
            case PaddingMode::OAEP:
                return RSA_PKCS1_OAEP_PADDING;
            default:
                throw DualRSAException("Unsupported padding mode");
        }
    }
    
    // 计算最大明文长度
    size_t calculateMaxPlaintextLength(int key_size_bits, PaddingMode padding) {
        int key_size_bytes = key_size_bits / 8;
        
        switch (padding) {
            case PaddingMode::PKCS1:
                // PKCS#1 v1.5: 需要11字节的填充开销
                return key_size_bytes - 11;
            case PaddingMode::OAEP:
                // OAEP: 需要42字节的填充开销（SHA-1）
                return key_size_bytes - 42;
            default:
                throw DualRSAException("Unsupported padding mode");
        }
    }
    
    // RSA加密
    std::vector<uint8_t> rsaEncrypt(const std::vector<uint8_t>& plaintext,
                                   const RSAPublicKey& public_key,
                                   PaddingMode padding) {
        EVP_PKEY_CTX* ctx = EVP_PKEY_CTX_new(public_key.getEVP_PKEY(), nullptr);
        if (!ctx) {
            throw DualRSAException("Failed to create EVP_PKEY_CTX: " + getOpenSSLError());
        }
        
        if (EVP_PKEY_encrypt_init(ctx) <= 0) {
            EVP_PKEY_CTX_free(ctx);
            throw DualRSAException("Failed to initialize encryption: " + getOpenSSLError());
        }
        
        // 设置填充模式
        int pad_type = getPaddingType(padding);
        if (EVP_PKEY_CTX_set_rsa_padding(ctx, pad_type) <= 0) {
            EVP_PKEY_CTX_free(ctx);
            throw DualRSAException("Failed to set padding mode: " + getOpenSSLError());
        }
        
        // 获取输出长度
        size_t outlen;
        if (EVP_PKEY_encrypt(ctx, nullptr, &outlen, plaintext.data(), plaintext.size()) <= 0) {
            EVP_PKEY_CTX_free(ctx);
            throw DualRSAException("Failed to determine output length: " + getOpenSSLError());
        }
        
        // 执行加密
        std::vector<uint8_t> ciphertext(outlen);
        if (EVP_PKEY_encrypt(ctx, ciphertext.data(), &outlen, plaintext.data(), plaintext.size()) <= 0) {
            EVP_PKEY_CTX_free(ctx);
            throw DualRSAException("Failed to encrypt: " + getOpenSSLError());
        }
        
        EVP_PKEY_CTX_free(ctx);
        ciphertext.resize(outlen);
        return ciphertext;
    }
    
    // RSA解密
    std::vector<uint8_t> rsaDecrypt(const std::vector<uint8_t>& ciphertext,
                                   const RSAPrivateKey& private_key,
                                   PaddingMode padding) {
        EVP_PKEY_CTX* ctx = EVP_PKEY_CTX_new(private_key.getEVP_PKEY(), nullptr);
        if (!ctx) {
            throw DualRSAException("Failed to create EVP_PKEY_CTX: " + getOpenSSLError());
        }
        
        if (EVP_PKEY_decrypt_init(ctx) <= 0) {
            EVP_PKEY_CTX_free(ctx);
            throw DualRSAException("Failed to initialize decryption: " + getOpenSSLError());
        }
        
        // 设置填充模式
        int pad_type = getPaddingType(padding);
        if (EVP_PKEY_CTX_set_rsa_padding(ctx, pad_type) <= 0) {
            EVP_PKEY_CTX_free(ctx);
            throw DualRSAException("Failed to set padding mode: " + getOpenSSLError());
        }
        
        // 获取输出长度
        size_t outlen;
        if (EVP_PKEY_decrypt(ctx, nullptr, &outlen, ciphertext.data(), ciphertext.size()) <= 0) {
            EVP_PKEY_CTX_free(ctx);
            throw DualRSAException("Failed to determine output length: " + getOpenSSLError());
        }
        
        // 执行解密
        std::vector<uint8_t> plaintext(outlen);
        if (EVP_PKEY_decrypt(ctx, plaintext.data(), &outlen, ciphertext.data(), ciphertext.size()) <= 0) {
            EVP_PKEY_CTX_free(ctx);
            throw DualRSAException("Failed to decrypt: " + getOpenSSLError());
        }
        
        EVP_PKEY_CTX_free(ctx);
        plaintext.resize(outlen);
        return plaintext;
    }
    
    // 字节数组转换为BIGNUM
    BIGNUM* bytesToBignum(const std::vector<uint8_t>& bytes) {
        return BN_bin2bn(bytes.data(), bytes.size(), nullptr);
    }
    
    // BIGNUM转换为字节数组
    std::vector<uint8_t> bignumToBytes(const BIGNUM* bn, size_t target_size = 0) {
        if (!bn) {
            throw DualRSAException("Invalid BIGNUM pointer");
        }
        
        int size = BN_num_bytes(bn);
        if (target_size > 0 && static_cast<size_t>(size) < target_size) {
            // 如果需要，在前面补零
            std::vector<uint8_t> result(target_size, 0);
            BN_bn2bin(bn, result.data() + (target_size - size));
            return result;
        } else {
            std::vector<uint8_t> result(size);
            BN_bn2bin(bn, result.data());
            return result;
        }
    }
}

// DualRSAEncryptor::Impl 实现
class DualRSAEncryptor::Impl {
public:
    Impl() = default;
    ~Impl() = default;
    
    std::vector<uint8_t> encrypt_dual(
        const std::vector<uint8_t>& plaintext1,
        const std::vector<uint8_t>& plaintext2,
        const RSAPublicKey& public_key1,
        const RSAPublicKey& public_key2,
        PaddingMode padding
    ) {
        // 验证密钥对
        if (!DualRSAEncryptor::validateKeyPair(public_key1, public_key2)) {
            throw DualRSAException("Public keys are not suitable for dual encryption (moduli not coprime)");
        }
        
        // 验证明文长度
        size_t max_len1 = DualRSAEncryptor::getMaxPlaintextLength(public_key1, padding);
        size_t max_len2 = DualRSAEncryptor::getMaxPlaintextLength(public_key2, padding);
        
        if (plaintext1.size() > max_len1) {
            throw DualRSAException("Plaintext1 too long for key1");
        }
        if (plaintext2.size() > max_len2) {
            throw DualRSAException("Plaintext2 too long for key2");
        }
        
        // 使用各自的公钥加密明文
        std::vector<uint8_t> c1 = rsaEncrypt(plaintext1, public_key1, padding);
        std::vector<uint8_t> c2 = rsaEncrypt(plaintext2, public_key2, padding);
        
        // 获取模数
        std::vector<uint8_t> n1 = public_key1.getModulus();
        std::vector<uint8_t> n2 = public_key2.getModulus();
        
        // 使用中国剩余定理求解统一密文
        std::vector<uint8_t> unified_ciphertext = solveCRT(c1, n1, c2, n2);
        
        // 验证CRT解的正确性
        if (!verifyCRT(unified_ciphertext, c1, n1, c2, n2)) {
            throw DualRSAException("CRT solution verification failed");
        }
        
        return unified_ciphertext;
    }
    
    std::vector<uint8_t> decrypt_single(
        const std::vector<uint8_t>& ciphertext,
        const RSAPrivateKey& private_key,
        PaddingMode padding
    ) {
        // 获取私钥对应的模数
        std::vector<uint8_t> modulus = private_key.getModulus();
        
        // 计算 ciphertext mod modulus 得到对应的中间密文
        BIGNUM* bn_ciphertext = bytesToBignum(ciphertext);
        BIGNUM* bn_modulus = bytesToBignum(modulus);
        BIGNUM* bn_intermediate = BN_new();
        BN_CTX* ctx = BN_CTX_new();
        
        if (!bn_ciphertext || !bn_modulus || !bn_intermediate || !ctx) {
            if (bn_ciphertext) BN_free(bn_ciphertext);
            if (bn_modulus) BN_free(bn_modulus);
            if (bn_intermediate) BN_free(bn_intermediate);
            if (ctx) BN_CTX_free(ctx);
            throw DualRSAException("Failed to create BIGNUM structures");
        }
        
        if (BN_mod(bn_intermediate, bn_ciphertext, bn_modulus, ctx) != 1) {
            BN_free(bn_ciphertext);
            BN_free(bn_modulus);
            BN_free(bn_intermediate);
            BN_CTX_free(ctx);
            throw DualRSAException("Failed to compute intermediate ciphertext");
        }
        
        // 转换为字节数组，确保长度与密钥长度匹配
        size_t key_size_bytes = private_key.getKeySize() / 8;
        std::vector<uint8_t> intermediate_ciphertext = bignumToBytes(bn_intermediate, key_size_bytes);
        
        BN_free(bn_ciphertext);
        BN_free(bn_modulus);
        BN_free(bn_intermediate);
        BN_CTX_free(ctx);
        
        // 使用私钥解密中间密文
        return rsaDecrypt(intermediate_ciphertext, private_key, padding);
    }
};

// DualRSAEncryptor 实现
DualRSAEncryptor::DualRSAEncryptor() : pImpl(std::make_unique<Impl>()) {}

DualRSAEncryptor::~DualRSAEncryptor() = default;

DualRSAEncryptor::DualRSAEncryptor(DualRSAEncryptor&& other) noexcept 
    : pImpl(std::move(other.pImpl)) {}

DualRSAEncryptor& DualRSAEncryptor::operator=(DualRSAEncryptor&& other) noexcept {
    if (this != &other) {
        pImpl = std::move(other.pImpl);
    }
    return *this;
}

std::vector<uint8_t> DualRSAEncryptor::encrypt_dual(
    const std::vector<uint8_t>& plaintext1,
    const std::vector<uint8_t>& plaintext2,
    const RSAPublicKey& public_key1,
    const RSAPublicKey& public_key2,
    PaddingMode padding
) {
    return pImpl->encrypt_dual(plaintext1, plaintext2, public_key1, public_key2, padding);
}

std::vector<uint8_t> DualRSAEncryptor::decrypt_single(
    const std::vector<uint8_t>& ciphertext,
    const RSAPrivateKey& private_key,
    PaddingMode padding
) {
    return pImpl->decrypt_single(ciphertext, private_key, padding);
}

bool DualRSAEncryptor::validateKeyPair(const RSAPublicKey& public_key1, 
                                      const RSAPublicKey& public_key2) {
    try {
        std::vector<uint8_t> n1 = public_key1.getModulus();
        std::vector<uint8_t> n2 = public_key2.getModulus();
        return areModuliCoprime(n1, n2);
    } catch (const std::exception&) {
        return false;
    }
}

size_t DualRSAEncryptor::getMaxPlaintextLength(const RSAPublicKey& public_key, 
                                              PaddingMode padding) {
    return calculateMaxPlaintextLength(public_key.getKeySize(), padding);
}

size_t DualRSAEncryptor::getCiphertextLength(const RSAPublicKey& public_key) {
    return public_key.getKeySize() / 8;
}

// 便利函数实现
std::vector<uint8_t> encrypt_dual(
    const std::vector<uint8_t>& plaintext1,
    const std::vector<uint8_t>& plaintext2,
    const RSAPublicKey& public_key1,
    const RSAPublicKey& public_key2,
    PaddingMode padding
) {
    DualRSAEncryptor encryptor;
    return encryptor.encrypt_dual(plaintext1, plaintext2, public_key1, public_key2, padding);
}

std::vector<uint8_t> decrypt_single(
    const std::vector<uint8_t>& ciphertext,
    const RSAPrivateKey& private_key,
    PaddingMode padding
) {
    DualRSAEncryptor encryptor;
    return encryptor.decrypt_single(ciphertext, private_key, padding);
}

} // namespace dual_rsa_crypto
