# CMAKE generated file: DO NOT EDIT!
# Generated by "MinGW Makefiles" Generator, CMake Version 3.30

# Delete rule output on recipe failure.
.DELETE_ON_ERROR:

#=============================================================================
# Special targets provided by cmake.

# Disable implicit rules so canonical targets will work.
.SUFFIXES:

# Disable VCS-based implicit rules.
% : %,v

# Disable VCS-based implicit rules.
% : RCS/%

# Disable VCS-based implicit rules.
% : RCS/%,v

# Disable VCS-based implicit rules.
% : SCCS/s.%

# Disable VCS-based implicit rules.
% : s.%

.SUFFIXES: .hpux_make_needs_suffix_list

# Command-line flag to silence nested $(MAKE).
$(VERBOSE)MAKESILENT = -s

#Suppress display of executed commands.
$(VERBOSE).SILENT:

# A target that is always out of date.
cmake_force:
.PHONY : cmake_force

#=============================================================================
# Set environment variables for the build.

SHELL = cmd.exe

# The CMake executable.
CMAKE_COMMAND = E:\cmake\bin\cmake.exe

# The command to remove a file.
RM = E:\cmake\bin\cmake.exe -E rm -f

# Escaping for special characters.
EQUALS = =

# The top-level source directory on which CMake was run.
CMAKE_SOURCE_DIR = F:\Projects\DualKeyEncryption

# The top-level build directory on which CMake was run.
CMAKE_BINARY_DIR = F:\Projects\DualKeyEncryption\build

# Include any dependencies generated for this target.
include CMakeFiles/api_compatibility_test.dir/depend.make
# Include any dependencies generated by the compiler for this target.
include CMakeFiles/api_compatibility_test.dir/compiler_depend.make

# Include the progress variables for this target.
include CMakeFiles/api_compatibility_test.dir/progress.make

# Include the compile flags for this target's objects.
include CMakeFiles/api_compatibility_test.dir/flags.make

CMakeFiles/api_compatibility_test.dir/tests/api_compatibility_test.cpp.obj: CMakeFiles/api_compatibility_test.dir/flags.make
CMakeFiles/api_compatibility_test.dir/tests/api_compatibility_test.cpp.obj: CMakeFiles/api_compatibility_test.dir/includes_CXX.rsp
CMakeFiles/api_compatibility_test.dir/tests/api_compatibility_test.cpp.obj: F:/Projects/DualKeyEncryption/tests/api_compatibility_test.cpp
CMakeFiles/api_compatibility_test.dir/tests/api_compatibility_test.cpp.obj: CMakeFiles/api_compatibility_test.dir/compiler_depend.ts
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green --progress-dir=F:\Projects\DualKeyEncryption\build\CMakeFiles --progress-num=$(CMAKE_PROGRESS_1) "Building CXX object CMakeFiles/api_compatibility_test.dir/tests/api_compatibility_test.cpp.obj"
	D:\msys64\mingw64\bin\g++.exe $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -MD -MT CMakeFiles/api_compatibility_test.dir/tests/api_compatibility_test.cpp.obj -MF CMakeFiles\api_compatibility_test.dir\tests\api_compatibility_test.cpp.obj.d -o CMakeFiles\api_compatibility_test.dir\tests\api_compatibility_test.cpp.obj -c F:\Projects\DualKeyEncryption\tests\api_compatibility_test.cpp

CMakeFiles/api_compatibility_test.dir/tests/api_compatibility_test.cpp.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Preprocessing CXX source to CMakeFiles/api_compatibility_test.dir/tests/api_compatibility_test.cpp.i"
	D:\msys64\mingw64\bin\g++.exe $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -E F:\Projects\DualKeyEncryption\tests\api_compatibility_test.cpp > CMakeFiles\api_compatibility_test.dir\tests\api_compatibility_test.cpp.i

CMakeFiles/api_compatibility_test.dir/tests/api_compatibility_test.cpp.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Compiling CXX source to assembly CMakeFiles/api_compatibility_test.dir/tests/api_compatibility_test.cpp.s"
	D:\msys64\mingw64\bin\g++.exe $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -S F:\Projects\DualKeyEncryption\tests\api_compatibility_test.cpp -o CMakeFiles\api_compatibility_test.dir\tests\api_compatibility_test.cpp.s

# Object files for target api_compatibility_test
api_compatibility_test_OBJECTS = \
"CMakeFiles/api_compatibility_test.dir/tests/api_compatibility_test.cpp.obj"

# External object files for target api_compatibility_test
api_compatibility_test_EXTERNAL_OBJECTS =

api_compatibility_test.exe: CMakeFiles/api_compatibility_test.dir/tests/api_compatibility_test.cpp.obj
api_compatibility_test.exe: CMakeFiles/api_compatibility_test.dir/build.make
api_compatibility_test.exe: libdual_rsa_crypto.a
api_compatibility_test.exe: D:/msys64/mingw64/lib/libssl.dll.a
api_compatibility_test.exe: D:/msys64/mingw64/lib/libcrypto.dll.a
api_compatibility_test.exe: CMakeFiles/api_compatibility_test.dir/linkLibs.rsp
api_compatibility_test.exe: CMakeFiles/api_compatibility_test.dir/objects1.rsp
api_compatibility_test.exe: CMakeFiles/api_compatibility_test.dir/link.txt
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green --bold --progress-dir=F:\Projects\DualKeyEncryption\build\CMakeFiles --progress-num=$(CMAKE_PROGRESS_2) "Linking CXX executable api_compatibility_test.exe"
	$(CMAKE_COMMAND) -E cmake_link_script CMakeFiles\api_compatibility_test.dir\link.txt --verbose=$(VERBOSE)

# Rule to build all files generated by this target.
CMakeFiles/api_compatibility_test.dir/build: api_compatibility_test.exe
.PHONY : CMakeFiles/api_compatibility_test.dir/build

CMakeFiles/api_compatibility_test.dir/clean:
	$(CMAKE_COMMAND) -P CMakeFiles\api_compatibility_test.dir\cmake_clean.cmake
.PHONY : CMakeFiles/api_compatibility_test.dir/clean

CMakeFiles/api_compatibility_test.dir/depend:
	$(CMAKE_COMMAND) -E cmake_depends "MinGW Makefiles" F:\Projects\DualKeyEncryption F:\Projects\DualKeyEncryption F:\Projects\DualKeyEncryption\build F:\Projects\DualKeyEncryption\build F:\Projects\DualKeyEncryption\build\CMakeFiles\api_compatibility_test.dir\DependInfo.cmake "--color=$(COLOR)"
.PHONY : CMakeFiles/api_compatibility_test.dir/depend

