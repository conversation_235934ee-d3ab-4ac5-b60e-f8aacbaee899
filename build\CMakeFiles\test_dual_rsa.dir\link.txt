E:\cmake\bin\cmake.exe -E rm -f CMake<PERSON>iles\test_dual_rsa.dir/objects.a
D:\msys64\mingw64\bin\ar.exe qc CMakeFiles\test_dual_rsa.dir/objects.a @CMakeFiles\test_dual_rsa.dir\objects1.rsp
D:\msys64\mingw64\bin\g++.exe  -Wall -Wextra -Wpedantic -Wl,--whole-archive CMakeFiles\test_dual_rsa.dir/objects.a -Wl,--no-whole-archive -o test_dual_rsa.exe -Wl,--out-implib,libtest_dual_rsa.dll.a -Wl,--major-image-version,0,--minor-image-version,0 @CMakeFiles\test_dual_rsa.dir\linkLibs.rsp
