# CMake generated Testfile for 
# Source directory: F:/Projects/DualKeyEncryption
# Build directory: F:/Projects/DualKeyEncryption/build
# 
# This file includes the relevant testing commands required for 
# testing this directory and lists subdirectories to be tested as well.
add_test(DualRSATests "F:/Projects/DualKeyEncryption/build/test_dual_rsa.exe")
set_tests_properties(DualRSATests PROPERTIES  _BACKTRACE_TRIPLES "F:/Projects/DualKeyEncryption/CMakeLists.txt;65;add_test;F:/Projects/DualKeyEncryption/CMakeLists.txt;0;")
add_test(SimpleTest "F:/Projects/DualKeyEncryption/build/simple_test.exe")
set_tests_properties(SimpleTest PROPERTIES  _BACKTRACE_TRIPLES "F:/Projects/DualKeyEncryption/CMakeLists.txt;66;add_test;F:/Projects/DualKeyEncryption/CMakeLists.txt;0;")
