#----------------------------------------------------------------
# Generated CMake target import file.
#----------------------------------------------------------------

# Commands may need to know the format version.
set(CMAKE_IMPORT_FILE_VERSION 1)

# Import target "DualRSACrypto::dual_rsa_crypto" for configuration ""
set_property(TARGET DualRSACrypto::dual_rsa_crypto APPEND PROPERTY IMPORTED_CONFIGURATIONS NOCONFIG)
set_target_properties(DualRSACrypto::dual_rsa_crypto PROPERTIES
  IMPORTED_LINK_INTERFACE_LANGUAGES_NOCONFIG "CXX"
  IMPORTED_LOCATION_NOCONFIG "${_IMPORT_PREFIX}/lib/libdual_rsa_crypto.a"
  )

list(APPEND _cmake_import_check_targets DualRSACrypto::dual_rsa_crypto )
list(APPEND _cmake_import_check_files_for_DualRSACrypto::dual_rsa_crypto "${_IMPORT_PREFIX}/lib/libdual_rsa_crypto.a" )

# Commands beyond this point should not need to know the version.
set(CMAKE_IMPORT_FILE_VERSION)
