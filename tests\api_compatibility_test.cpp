#include "dual_rsa_crypto/dual_rsa_crypto.hpp"
#include <iostream>
#include <string>
#include <iomanip>

using namespace dual_rsa_crypto;

void printHex(const std::vector<uint8_t>& data, const std::string& label) {
    std::cout << label << " (" << data.size() << " bytes): ";
    for (size_t i = 0; i < std::min(data.size(), size_t(16)); ++i) {
        std::cout << std::hex << std::setw(2) << std::setfill('0') << static_cast<int>(data[i]) << " ";
    }
    if (data.size() > 16) {
        std::cout << "...";
    }
    std::cout << std::dec << std::endl;
}

int main() {
    try {
        std::cout << "=== API Compatibility Test ===" << std::endl;
        
        // Test key generation and parameter extraction
        std::cout << "\n1. Testing key generation and parameter extraction..." << std::endl;
        auto [private_key1, public_key1] = generateRSAKeyPair(1024);
        auto [private_key2, public_key2] = generateRSAKeyPair(1024);
        
        std::cout << "Key1 size: " << public_key1.getKeySize() << " bits" << std::endl;
        std::cout << "Key2 size: " << public_key2.getKeySize() << " bits" << std::endl;
        
        // Test parameter extraction (using modern OpenSSL 3.0 API internally)
        auto n1 = public_key1.getModulus();
        auto e1 = public_key1.getPublicExponent();
        auto n2 = public_key2.getModulus();
        auto d1 = private_key1.getPrivateExponent();
        
        printHex(n1, "Key1 Modulus");
        printHex(e1, "Key1 Public Exponent");
        printHex(n2, "Key2 Modulus");
        printHex(d1, "Key1 Private Exponent");
        
        // Test moduli coprimality
        std::cout << "\n2. Testing moduli coprimality..." << std::endl;
        bool coprime = areModuliCoprime(n1, n2);
        std::cout << "Moduli are coprime: " << (coprime ? "Yes" : "No") << std::endl;
        
        // Test public key extraction from private key
        std::cout << "\n3. Testing public key extraction..." << std::endl;
        RSAPublicKey extracted_pub = private_key1.getPublicKey();
        auto extracted_n = extracted_pub.getModulus();
        auto extracted_e = extracted_pub.getPublicExponent();
        
        std::cout << "Original and extracted moduli match: " << (n1 == extracted_n ? "Yes" : "No") << std::endl;
        std::cout << "Original and extracted exponents match: " << (e1 == extracted_e ? "Yes" : "No") << std::endl;
        
        // Test dual-key encryption/decryption
        std::cout << "\n4. Testing dual-key encryption/decryption..." << std::endl;
        std::string message1 = "Confidential data";
        std::string message2 = "Decoy information";
        
        std::vector<uint8_t> plaintext1(message1.begin(), message1.end());
        std::vector<uint8_t> plaintext2(message2.begin(), message2.end());
        
        // Test both padding modes
        for (auto padding : {PaddingMode::PKCS1, PaddingMode::OAEP}) {
            std::string padding_name = (padding == PaddingMode::PKCS1) ? "PKCS#1" : "OAEP";
            std::cout << "\nTesting " << padding_name << " padding:" << std::endl;
            
            auto ciphertext = encrypt_dual(plaintext1, plaintext2, public_key1, public_key2, padding);
            printHex(ciphertext, "Unified ciphertext");
            
            auto decrypted1 = decrypt_single(ciphertext, private_key1, padding);
            auto decrypted2 = decrypt_single(ciphertext, private_key2, padding);
            
            std::string recovered1(decrypted1.begin(), decrypted1.end());
            std::string recovered2(decrypted2.begin(), decrypted2.end());
            
            std::cout << "Decrypted message 1: " << recovered1 << std::endl;
            std::cout << "Decrypted message 2: " << recovered2 << std::endl;
            
            bool success = (recovered1 == message1) && (recovered2 == message2);
            std::cout << padding_name << " test: " << (success ? "PASSED" : "FAILED") << std::endl;
        }
        
        // Test key validation
        std::cout << "\n5. Testing key validation..." << std::endl;
        bool valid_pair = DualRSAEncryptor::validateKeyPair(public_key1, public_key2);
        bool invalid_pair = DualRSAEncryptor::validateKeyPair(public_key1, public_key1);
        
        std::cout << "Different keys validation: " << (valid_pair ? "PASSED" : "FAILED") << std::endl;
        std::cout << "Same keys validation: " << (!invalid_pair ? "PASSED" : "FAILED") << std::endl;
        
        // Test utility functions
        std::cout << "\n6. Testing utility functions..." << std::endl;
        size_t max_pkcs1 = DualRSAEncryptor::getMaxPlaintextLength(public_key1, PaddingMode::PKCS1);
        size_t max_oaep = DualRSAEncryptor::getMaxPlaintextLength(public_key1, PaddingMode::OAEP);
        size_t cipher_len = DualRSAEncryptor::getCiphertextLength(public_key1);
        
        std::cout << "Max plaintext length (PKCS#1): " << max_pkcs1 << " bytes" << std::endl;
        std::cout << "Max plaintext length (OAEP): " << max_oaep << " bytes" << std::endl;
        std::cout << "Ciphertext length: " << cipher_len << " bytes" << std::endl;
        
        std::cout << "\n=== All API compatibility tests PASSED! ===" << std::endl;
        std::cout << "The updated OpenSSL 3.0 implementation maintains full API compatibility." << std::endl;
        
        return 0;
        
    } catch (const std::exception& e) {
        std::cerr << "API compatibility test failed: " << e.what() << std::endl;
        return 1;
    }
}
