{"cmake": {"generator": {"multiConfig": false, "name": "MinGW Makefiles"}, "paths": {"cmake": "E:/cmake/bin/cmake.exe", "cpack": "E:/cmake/bin/cpack.exe", "ctest": "E:/cmake/bin/ctest.exe", "root": "E:/cmake/share/cmake-3.30"}, "version": {"isDirty": false, "major": 3, "minor": 30, "patch": 3, "string": "3.30.3", "suffix": ""}}, "objects": [{"jsonFile": "codemodel-v2-a1f73ae2db560e99b1e6.json", "kind": "codemodel", "version": {"major": 2, "minor": 7}}, {"jsonFile": "cache-v2-363ece4abdc56fe8f258.json", "kind": "cache", "version": {"major": 2, "minor": 0}}, {"jsonFile": "cmakeFiles-v1-3b11559e5bf6d1a021c0.json", "kind": "cmakeFiles", "version": {"major": 1, "minor": 1}}, {"jsonFile": "toolchains-v1-f4f621ddeef8dd6ece7a.json", "kind": "toolchains", "version": {"major": 1, "minor": 0}}], "reply": {"client-vscode": {"query.json": {"requests": [{"kind": "cache", "version": 2}, {"kind": "codemodel", "version": 2}, {"kind": "toolchains", "version": 1}, {"kind": "cmakeFiles", "version": 1}], "responses": [{"jsonFile": "cache-v2-363ece4abdc56fe8f258.json", "kind": "cache", "version": {"major": 2, "minor": 0}}, {"jsonFile": "codemodel-v2-a1f73ae2db560e99b1e6.json", "kind": "codemodel", "version": {"major": 2, "minor": 7}}, {"jsonFile": "toolchains-v1-f4f621ddeef8dd6ece7a.json", "kind": "toolchains", "version": {"major": 1, "minor": 0}}, {"jsonFile": "cmakeFiles-v1-3b11559e5bf6d1a021c0.json", "kind": "cmakeFiles", "version": {"major": 1, "minor": 1}}]}}}}