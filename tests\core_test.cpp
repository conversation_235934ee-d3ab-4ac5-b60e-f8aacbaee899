#include "dual_rsa_crypto/dual_rsa_crypto.hpp"
#include <iostream>
#include <cassert>
#include <string>
#include <vector>
#include <chrono>
#include <random>

using namespace dual_rsa_crypto;

// Test helper functions
namespace {
    // Generate random byte array
    std::vector<uint8_t> generateRandomBytes(size_t length) {
        std::random_device rd;
        std::mt19937 gen(rd());
        std::uniform_int_distribution<uint8_t> dis(0, 255);
        
        std::vector<uint8_t> result(length);
        for (size_t i = 0; i < length; ++i) {
            result[i] = dis(gen);
        }
        return result;
    }
    
    // String to byte array
    std::vector<uint8_t> stringToBytes(const std::string& str) {
        return std::vector<uint8_t>(str.begin(), str.end());
    }
    
    // Byte array to string
    std::string bytesToString(const std::vector<uint8_t>& bytes) {
        return std::string(bytes.begin(), bytes.end());
    }
    
    // Test timer
    class Timer {
    public:
        Timer(const std::string& name) : name_(name), start_(std::chrono::high_resolution_clock::now()) {}
        
        ~Timer() {
            auto end = std::chrono::high_resolution_clock::now();
            auto duration = std::chrono::duration_cast<std::chrono::milliseconds>(end - start_);
            std::cout << name_ << " took " << duration.count() << " ms" << std::endl;
        }
        
    private:
        std::string name_;
        std::chrono::high_resolution_clock::time_point start_;
    };
}

// Core test functions
class CoreTest {
public:
    static void runAllTests() {
        std::cout << "=== Core Dual-Key RSA Tests ===" << std::endl;
        
        testKeyGeneration();
        testBasicEncryptionDecryption();
        testDifferentPaddingModes();
        testErrorHandling();
        testCRTSolver();
        
        std::cout << "\n=== All core tests passed! ===" << std::endl;
    }
    
private:
    static void testKeyGeneration() {
        std::cout << "\n--- Testing Key Generation ---" << std::endl;
        Timer timer("Key generation test");
        
        std::cout << "Generating 1024 bit key pairs..." << std::endl;
        auto [private_key1, public_key1] = generateRSAKeyPair(1024);
        auto [private_key2, public_key2] = generateRSAKeyPair(1024);
        
        // Verify key properties
        assert(public_key1.getKeySize() == 1024);
        assert(public_key2.getKeySize() == 1024);
        assert(private_key1.getKeySize() == 1024);
        assert(private_key2.getKeySize() == 1024);
        
        // Verify moduli are different and coprime
        auto n1 = public_key1.getModulus();
        auto n2 = public_key2.getModulus();
        assert(n1 != n2);
        assert(areModuliCoprime(n1, n2));
        
        std::cout << "✓ Key generation successful" << std::endl;
    }
    
    static void testBasicEncryptionDecryption() {
        std::cout << "\n--- Testing Basic Encryption/Decryption ---" << std::endl;
        Timer timer("Basic encryption/decryption test");
        
        // Generate key pairs
        auto [private_key1, public_key1] = generateRSAKeyPair(1024);
        auto [private_key2, public_key2] = generateRSAKeyPair(1024);
        
        // Prepare test data
        std::string message1 = "Secret message 1";
        std::string message2 = "Secret message 2";
        
        auto plaintext1 = stringToBytes(message1);
        auto plaintext2 = stringToBytes(message2);
        
        std::cout << "Original message 1: " << message1 << std::endl;
        std::cout << "Original message 2: " << message2 << std::endl;
        
        // Dual-key encryption
        DualRSAEncryptor encryptor;
        auto ciphertext = encryptor.encrypt_dual(plaintext1, plaintext2, public_key1, public_key2);
        
        std::cout << "Unified ciphertext length: " << ciphertext.size() << " bytes" << std::endl;
        
        // Decrypt with first private key
        auto decrypted1 = encryptor.decrypt_single(ciphertext, private_key1);
        std::string recovered1 = bytesToString(decrypted1);
        
        // Decrypt with second private key
        auto decrypted2 = encryptor.decrypt_single(ciphertext, private_key2);
        std::string recovered2 = bytesToString(decrypted2);
        
        std::cout << "Decrypted message 1: " << recovered1 << std::endl;
        std::cout << "Decrypted message 2: " << recovered2 << std::endl;
        
        // Verify decryption results
        assert(recovered1 == message1);
        assert(recovered2 == message2);
        
        std::cout << "✓ Basic encryption/decryption test passed" << std::endl;
    }
    
    static void testDifferentPaddingModes() {
        std::cout << "\n--- Testing Different Padding Modes ---" << std::endl;
        Timer timer("Different padding modes test");
        
        auto [private_key1, public_key1] = generateRSAKeyPair(1024);
        auto [private_key2, public_key2] = generateRSAKeyPair(1024);
        
        std::string message1 = "PKCS1 test message";
        std::string message2 = "OAEP test message";
        
        auto plaintext1 = stringToBytes(message1);
        auto plaintext2 = stringToBytes(message2);
        
        // Test PKCS1 padding
        {
            std::cout << "Testing PKCS#1 padding..." << std::endl;
            auto ciphertext = encrypt_dual(plaintext1, plaintext2, public_key1, public_key2, PaddingMode::PKCS1);
            auto decrypted1 = decrypt_single(ciphertext, private_key1, PaddingMode::PKCS1);
            auto decrypted2 = decrypt_single(ciphertext, private_key2, PaddingMode::PKCS1);
            
            assert(bytesToString(decrypted1) == message1);
            assert(bytesToString(decrypted2) == message2);
            std::cout << "✓ PKCS#1 padding test passed" << std::endl;
        }
        
        // Test OAEP padding
        {
            std::cout << "Testing OAEP padding..." << std::endl;
            auto ciphertext = encrypt_dual(plaintext1, plaintext2, public_key1, public_key2, PaddingMode::OAEP);
            auto decrypted1 = decrypt_single(ciphertext, private_key1, PaddingMode::OAEP);
            auto decrypted2 = decrypt_single(ciphertext, private_key2, PaddingMode::OAEP);
            
            assert(bytesToString(decrypted1) == message1);
            assert(bytesToString(decrypted2) == message2);
            std::cout << "✓ OAEP padding test passed" << std::endl;
        }
    }
    
    static void testErrorHandling() {
        std::cout << "\n--- Testing Error Handling ---" << std::endl;
        Timer timer("Error handling test");
        
        auto [private_key1, public_key1] = generateRSAKeyPair(1024);
        auto [private_key2, public_key2] = generateRSAKeyPair(1024);
        
        // Test message too long
        size_t max_len = DualRSAEncryptor::getMaxPlaintextLength(public_key1, PaddingMode::PKCS1);
        auto too_long_message = generateRandomBytes(max_len + 1);
        auto normal_message = generateRandomBytes(10);
        
        try {
            encrypt_dual(too_long_message, normal_message, public_key1, public_key2);
            assert(false); // Should throw exception
        } catch (const DualRSAException& e) {
            std::cout << "✓ Correctly caught message too long exception: " << e.what() << std::endl;
        }
        
        // Test invalid ciphertext decryption
        std::vector<uint8_t> invalid_ciphertext(private_key1.getKeySize() / 8, 0xFF);
        try {
            decrypt_single(invalid_ciphertext, private_key1);
            std::cout << "Warning: Decrypting invalid ciphertext did not throw exception (OpenSSL tolerance)" << std::endl;
        } catch (const DualRSAException& e) {
            std::cout << "✓ Correctly caught invalid ciphertext exception: " << e.what() << std::endl;
        } catch (const std::exception& e) {
            std::cout << "✓ Caught exception: " << e.what() << std::endl;
        }
    }
    
    static void testCRTSolver() {
        std::cout << "\n--- Testing Chinese Remainder Theorem Solver ---" << std::endl;
        Timer timer("CRT solver test");
        
        // Use small test data
        std::vector<uint8_t> a1 = {2};  // 2
        std::vector<uint8_t> n1 = {3};  // 3
        std::vector<uint8_t> a2 = {3};  // 3
        std::vector<uint8_t> n2 = {5};  // 5
        
        // Solve x ≡ 2 (mod 3), x ≡ 3 (mod 5)
        // Solution should be x = 8 (mod 15)
        auto solution = solveCRT(a1, n1, a2, n2);
        
        // Verify solution
        assert(verifyCRT(solution, a1, n1, a2, n2));
        
        std::cout << "✓ CRT solver test passed" << std::endl;
    }
};

int main() {
    try {
        CoreTest::runAllTests();
        return 0;
    } catch (const std::exception& e) {
        std::cerr << "Test failed: " << e.what() << std::endl;
        return 1;
    }
}
