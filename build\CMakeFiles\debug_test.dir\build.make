# CMAKE generated file: DO NOT EDIT!
# Generated by "MinGW Makefiles" Generator, CMake Version 3.30

# Delete rule output on recipe failure.
.DELETE_ON_ERROR:

#=============================================================================
# Special targets provided by cmake.

# Disable implicit rules so canonical targets will work.
.SUFFIXES:

# Disable VCS-based implicit rules.
% : %,v

# Disable VCS-based implicit rules.
% : RCS/%

# Disable VCS-based implicit rules.
% : RCS/%,v

# Disable VCS-based implicit rules.
% : SCCS/s.%

# Disable VCS-based implicit rules.
% : s.%

.SUFFIXES: .hpux_make_needs_suffix_list

# Command-line flag to silence nested $(MAKE).
$(VERBOSE)MAKESILENT = -s

#Suppress display of executed commands.
$(VERBOSE).SILENT:

# A target that is always out of date.
cmake_force:
.PHONY : cmake_force

#=============================================================================
# Set environment variables for the build.

SHELL = cmd.exe

# The CMake executable.
CMAKE_COMMAND = E:\cmake\bin\cmake.exe

# The command to remove a file.
RM = E:\cmake\bin\cmake.exe -E rm -f

# Escaping for special characters.
EQUALS = =

# The top-level source directory on which CMake was run.
CMAKE_SOURCE_DIR = F:\Projects\DualKeyEncryption

# The top-level build directory on which CMake was run.
CMAKE_BINARY_DIR = F:\Projects\DualKeyEncryption\build

# Include any dependencies generated for this target.
include CMakeFiles/debug_test.dir/depend.make
# Include any dependencies generated by the compiler for this target.
include CMakeFiles/debug_test.dir/compiler_depend.make

# Include the progress variables for this target.
include CMakeFiles/debug_test.dir/progress.make

# Include the compile flags for this target's objects.
include CMakeFiles/debug_test.dir/flags.make

CMakeFiles/debug_test.dir/tests/debug_test.cpp.obj: CMakeFiles/debug_test.dir/flags.make
CMakeFiles/debug_test.dir/tests/debug_test.cpp.obj: CMakeFiles/debug_test.dir/includes_CXX.rsp
CMakeFiles/debug_test.dir/tests/debug_test.cpp.obj: F:/Projects/DualKeyEncryption/tests/debug_test.cpp
CMakeFiles/debug_test.dir/tests/debug_test.cpp.obj: CMakeFiles/debug_test.dir/compiler_depend.ts
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green --progress-dir=F:\Projects\DualKeyEncryption\build\CMakeFiles --progress-num=$(CMAKE_PROGRESS_1) "Building CXX object CMakeFiles/debug_test.dir/tests/debug_test.cpp.obj"
	D:\msys64\mingw64\bin\g++.exe $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -MD -MT CMakeFiles/debug_test.dir/tests/debug_test.cpp.obj -MF CMakeFiles\debug_test.dir\tests\debug_test.cpp.obj.d -o CMakeFiles\debug_test.dir\tests\debug_test.cpp.obj -c F:\Projects\DualKeyEncryption\tests\debug_test.cpp

CMakeFiles/debug_test.dir/tests/debug_test.cpp.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Preprocessing CXX source to CMakeFiles/debug_test.dir/tests/debug_test.cpp.i"
	D:\msys64\mingw64\bin\g++.exe $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -E F:\Projects\DualKeyEncryption\tests\debug_test.cpp > CMakeFiles\debug_test.dir\tests\debug_test.cpp.i

CMakeFiles/debug_test.dir/tests/debug_test.cpp.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Compiling CXX source to assembly CMakeFiles/debug_test.dir/tests/debug_test.cpp.s"
	D:\msys64\mingw64\bin\g++.exe $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -S F:\Projects\DualKeyEncryption\tests\debug_test.cpp -o CMakeFiles\debug_test.dir\tests\debug_test.cpp.s

# Object files for target debug_test
debug_test_OBJECTS = \
"CMakeFiles/debug_test.dir/tests/debug_test.cpp.obj"

# External object files for target debug_test
debug_test_EXTERNAL_OBJECTS =

debug_test.exe: CMakeFiles/debug_test.dir/tests/debug_test.cpp.obj
debug_test.exe: CMakeFiles/debug_test.dir/build.make
debug_test.exe: D:/msys64/mingw64/lib/libssl.dll.a
debug_test.exe: D:/msys64/mingw64/lib/libcrypto.dll.a
debug_test.exe: CMakeFiles/debug_test.dir/linkLibs.rsp
debug_test.exe: CMakeFiles/debug_test.dir/objects1.rsp
debug_test.exe: CMakeFiles/debug_test.dir/link.txt
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green --bold --progress-dir=F:\Projects\DualKeyEncryption\build\CMakeFiles --progress-num=$(CMAKE_PROGRESS_2) "Linking CXX executable debug_test.exe"
	$(CMAKE_COMMAND) -E cmake_link_script CMakeFiles\debug_test.dir\link.txt --verbose=$(VERBOSE)

# Rule to build all files generated by this target.
CMakeFiles/debug_test.dir/build: debug_test.exe
.PHONY : CMakeFiles/debug_test.dir/build

CMakeFiles/debug_test.dir/clean:
	$(CMAKE_COMMAND) -P CMakeFiles\debug_test.dir\cmake_clean.cmake
.PHONY : CMakeFiles/debug_test.dir/clean

CMakeFiles/debug_test.dir/depend:
	$(CMAKE_COMMAND) -E cmake_depends "MinGW Makefiles" F:\Projects\DualKeyEncryption F:\Projects\DualKeyEncryption F:\Projects\DualKeyEncryption\build F:\Projects\DualKeyEncryption\build F:\Projects\DualKeyEncryption\build\CMakeFiles\debug_test.dir\DependInfo.cmake "--color=$(COLOR)"
.PHONY : CMakeFiles/debug_test.dir/depend

